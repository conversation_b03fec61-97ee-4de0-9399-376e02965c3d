// ignore_for_file: prefer_const_constructors, avoid_unnecessary_containers

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/dashboard/mainboard.dart';
import 'package:silverleaf/controller/gallery_controller.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:ui' as ui;

import 'package:flutter/rendering.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:galleryimage/galleryimage.dart';
import 'dart:typed_data';
import 'package:photo_view/photo_view.dart';

// Professional design constants for Gallery
class GalleryColors {
  static const Color primary = Color(0xFF2E7D32);
  static const Color primaryLight = Color(0xFF4CAF50);
  static const Color secondary = Color(0xFF1976D2);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color background = Color(0xFFF8F9FA);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFF9E9E9E);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color divider = Color(0xFFE0E0E0);
  static const Color cardShadow = Color(0x1A000000);
  static const Color success = Color(0xFF4CAF50);
  static const Color error = Color(0xFFE53935);

  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFF2E7D32), Color(0xFF4CAF50)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}

class GalleryTextStyles {
  static const TextStyle headlineLarge = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w700,
    color: GalleryColors.textPrimary,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: GalleryColors.textPrimary,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: GalleryColors.textPrimary,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: GalleryColors.textPrimary,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: GalleryColors.textSecondary,
  );
}

var gallerypages = 0;

class GalleryMainScreen extends StatefulWidget {
  const GalleryMainScreen({super.key});

  @override
  State<GalleryMainScreen> createState() => _GalleryMainScreenState();
}

class _GalleryMainScreenState extends State<GalleryMainScreen> {
  //GalleryController galleryController = GalleryController();
  var jsonData;
  var list_data;
  List userNames = [];
  void initState() {
    // this.fetchApiData();
    super.initState();
    this.getListString();
    //this.jsonData = galleryController.fetchApiData();
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;
        this.fetchApiData();
      });
    }
  }

  Future<List> fetchApiData() async {
    print(userNames[14]);
    final response = await http.get(
      Uri.parse(
        'https://silverleafms.in/silvar_leaf/api/gallery/gallery-details/${userNames[14]}',
      ),
    );

    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);
      this.jsonData = responseBody['data'];
      //print(this.jsonData);
      return this.jsonData;
    } else {
      throw Exception('Failed to load data');
    }
  }

  back() {
    // print('fffff');
    // setState(() {
    //   globalBottomBarIndex = 0;
    //   Get.forceAppUpdate();
    //   Get.back();
    // });

    Get.back();
  }

  List galleryImage = ['images/inde.jpg', 'images/culturalsevent.jpg'];
  List galleryImageTitle = ['Independence Day', 'Cultural Event'];
  List subtitles = [
    'Independence day Flag Raising and Photos......',
    'Inter - Coimbatore Cultural Event Photos....',
  ];
  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: GalleryColors.primary, // Match the app bar green color
        statusBarIconBrightness:
            Brightness.light, // Light icons for dark status bar
        statusBarBrightness: Brightness.dark, // For iOS
      ),
    );

    return SafeArea(
      child: Scaffold(
        backgroundColor: GalleryColors.background,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: Container(
            decoration: const BoxDecoration(
              gradient: GalleryColors.primaryGradient,
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: IconButton(
                onPressed: back,
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: GalleryColors.textOnPrimary,
                  size: 24,
                ),
              ),
              title: Text(
                'Event Gallery',
                style: GalleryTextStyles.headlineSmall.copyWith(
                  color: GalleryColors.textOnPrimary,
                  fontWeight: FontWeight.w700,
                  fontSize: 22,
                ),
              ),
              centerTitle: true,
            ),
          ),
        ),
        body: galleryList(),
        bottomNavigationBar: Container(
          height: MediaQuery.of(context).padding.bottom,
          color: GalleryColors.background,
        ),
      ),
    );
  }

  Widget galleryList() {
    return FutureBuilder<List<dynamic>>(
      future: fetchApiData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(color: GalleryColors.primary),
                SizedBox(height: 2.0.hp),
                Text(
                  'Loading gallery...',
                  style: GalleryTextStyles.bodyMedium.copyWith(
                    color: GalleryColors.textSecondary,
                  ),
                ),
              ],
            ),
          );
        } else if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  color: GalleryColors.error,
                  size: 15.0.wp,
                ),
                SizedBox(height: 2.0.hp),
                Text(
                  'Failed to load gallery',
                  style: GalleryTextStyles.headlineSmall.copyWith(
                    color: GalleryColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.0.hp),
                Text(
                  'Please check your connection and try again',
                  style: GalleryTextStyles.bodyMedium.copyWith(
                    color: GalleryColors.textSecondary,
                  ),
                ),
              ],
            ),
          );
        } else {
          List<dynamic> data = snapshot.data!;
          if (data.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.photo_library_outlined,
                    color: GalleryColors.textHint,
                    size: 15.0.wp,
                  ),
                  SizedBox(height: 2.0.hp),
                  Text(
                    'No albums found',
                    style: GalleryTextStyles.headlineSmall.copyWith(
                      color: GalleryColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 1.0.hp),
                  Text(
                    'Check back later for new photo albums',
                    style: GalleryTextStyles.bodyMedium.copyWith(
                      color: GalleryColors.textSecondary,
                    ),
                  ),
                ],
              ),
            );
          } else {
            return ListView.separated(
              itemCount: data.length,
              shrinkWrap: true,
              padding: EdgeInsets.all(4.0.wp),
              separatorBuilder: (context, index) => SizedBox(height: 2.0.hp),
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: () {
                    Get.to(
                      GallerySecondScreen(
                        dataToReceive: jsonData[index]['album_title'],
                        description: jsonData[index]['description'],
                        user_id: jsonData[index]['id'],
                      ),
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: GalleryColors.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: GalleryColors.cardShadow,
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Image Section
                        Container(
                          height: 25.0.hp,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(16),
                              topRight: Radius.circular(16),
                            ),
                            color: GalleryColors.surfaceVariant,
                          ),
                          child: ClipRRect(
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(16),
                              topRight: Radius.circular(16),
                            ),
                            child: Image.network(
                              jsonData[index]['gallery_image'],
                              fit: BoxFit.cover,
                              loadingBuilder: (
                                context,
                                child,
                                loadingProgress,
                              ) {
                                if (loadingProgress == null) return child;
                                return Container(
                                  color: GalleryColors.surfaceVariant,
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      color: GalleryColors.primary,
                                    ),
                                  ),
                                );
                              },
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color: GalleryColors.surfaceVariant,
                                  child: Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.broken_image,
                                          color: GalleryColors.textHint,
                                          size: 8.0.wp,
                                        ),
                                        SizedBox(height: 1.0.hp),
                                        Text(
                                          'Image not available',
                                          style: GalleryTextStyles.bodySmall
                                              .copyWith(
                                                color: GalleryColors.textHint,
                                              ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),

                        // Content Section
                        Padding(
                          padding: EdgeInsets.all(4.0.wp),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                jsonData[index]['album_title'],
                                style: GalleryTextStyles.headlineSmall.copyWith(
                                  color: GalleryColors.primary,
                                  fontWeight: FontWeight.w600,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: 1.0.hp),
                              Text(
                                jsonData[index]['description'].toString(),
                                style: GalleryTextStyles.bodyMedium.copyWith(
                                  color: GalleryColors.textSecondary,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: 2.0.hp),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 3.0.wp,
                                      vertical: 1.0.hp,
                                    ),
                                    decoration: BoxDecoration(
                                      color: GalleryColors.primary.withValues(
                                        alpha: 0.1,
                                      ),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.photo_library,
                                          color: GalleryColors.primary,
                                          size: 4.0.wp,
                                        ),
                                        SizedBox(width: 1.0.wp),
                                        Text(
                                          "${jsonData[index]['gallery_image_count']} Photos",
                                          style: GalleryTextStyles.bodySmall
                                              .copyWith(
                                                color: GalleryColors.primary,
                                                fontWeight: FontWeight.w600,
                                              ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 3.0.wp,
                                      vertical: 1.0.hp,
                                    ),
                                    decoration: BoxDecoration(
                                      gradient: GalleryColors.primaryGradient,
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          "View All",
                                          style: GalleryTextStyles.bodySmall
                                              .copyWith(
                                                color:
                                                    GalleryColors.textOnPrimary,
                                                fontWeight: FontWeight.w600,
                                              ),
                                        ),
                                        SizedBox(width: 1.0.wp),
                                        Icon(
                                          Icons.arrow_forward_ios,
                                          color: GalleryColors.textOnPrimary,
                                          size: 3.0.wp,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          }
        }
      },
    );
  }

  Widget peopleContainer() {
    return Container(
      height: 2.6.hp,
      width: 4.0.wp,
      decoration: BoxDecoration(
        color: appcolor,
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 1),
      ),
    );
  }
}

class GallerySecondScreen extends StatefulWidget {
  final String dataToReceive;
  final String description;
  final int user_id;

  const GallerySecondScreen({
    super.key,
    required this.dataToReceive,
    required this.description,
    required this.user_id,
  });
  // GallerySecondScreen({
  //   super.key,
  // });

  @override
  State<GallerySecondScreen> createState() => _GallerySecondScreenState();
}

class _GallerySecondScreenState extends State<GallerySecondScreen> {
  String text = '';
  String description = '';

  subback() {
    // Get.to(const MainBoard());
    Get.back();
  }

  GalleryController galleryController = GalleryController();
  Future<List> fetchgalleryData(int id) async {
    print('tt');
    final url =
        'https://silverleafms.in/silvar_leaf/api/gallery/gallery-all-images/$id';

    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      final result = json.decode(response.body);

      return result['data'];
    } else {
      throw Exception('Failed to load data');
    }
  }

  String? firstHalf;
  String? secondHalf;

  bool flag = true;
  bool isExpanded = false;
  @override
  void initState() {
    super.initState();
    this.description = widget.description;
    this.text = widget.dataToReceive;

    //final response = this.galleryController.fetchgalleryData(widget.user_id);

    this.fetchgalleryData(widget.user_id);

    // if (this.description.length > 50) {
    //   firstHalf = this.description.substring(0, 90);
    //   secondHalf = this.description.substring(50, this.description.length);
    // } else {
    //   firstHalf = this.description;
    //   secondHalf = "";
    // }
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: GalleryColors.primary, // Match the app bar green color
        statusBarIconBrightness:
            Brightness.light, // Light icons for dark status bar
        statusBarBrightness: Brightness.dark, // For iOS
      ),
    );

    return WillPopScope(
      onWillPop: () => subback(),
      child: SafeArea(
        child: Scaffold(
          backgroundColor: GalleryColors.background,
          appBar: PreferredSize(
            preferredSize: Size(double.infinity, 9.0.hp),
            child: Container(
              decoration: const BoxDecoration(
                gradient: GalleryColors.primaryGradient,
              ),
              child: AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  onPressed: subback,
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: GalleryColors.textOnPrimary,
                    size: 24,
                  ),
                ),
                title: Text(
                  'Album Photos',
                  style: GalleryTextStyles.headlineSmall.copyWith(
                    color: GalleryColors.textOnPrimary,
                    fontWeight: FontWeight.w700,
                    fontSize: 22,
                  ),
                ),
                centerTitle: true,
              ),
            ),
          ),
          body: Column(
            children: [
              // Album Header Card
              Container(
                margin: EdgeInsets.all(4.0.wp),
                padding: EdgeInsets.all(4.0.wp),
                decoration: BoxDecoration(
                  color: GalleryColors.surface,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: GalleryColors.cardShadow,
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(2.0.wp),
                          decoration: BoxDecoration(
                            color: GalleryColors.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.photo_album,
                            color: GalleryColors.primary,
                            size: 6.0.wp,
                          ),
                        ),
                        SizedBox(width: 3.0.wp),
                        Expanded(
                          child: Text(
                            text,
                            style: GalleryTextStyles.headlineSmall.copyWith(
                              color: GalleryColors.primary,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (description.isNotEmpty) ...[
                      SizedBox(height: 2.0.hp),
                      Text(
                        description,
                        style: GalleryTextStyles.bodyMedium.copyWith(
                          color: GalleryColors.textSecondary,
                        ),
                        maxLines: isExpanded ? null : 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (description.length > 150) ...[
                        SizedBox(height: 1.0.hp),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              isExpanded = !isExpanded;
                            });
                          },
                          child: Text(
                            isExpanded ? 'Show Less' : 'Show More',
                            style: GalleryTextStyles.bodySmall.copyWith(
                              color: GalleryColors.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ],
                ),
              ),

              // Photos Grid
              Expanded(
                child: FutureBuilder<List<dynamic>>(
                  future: fetchgalleryData(widget.user_id),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(
                              color: GalleryColors.primary,
                            ),
                            SizedBox(height: 2.0.hp),
                            Text(
                              'Loading photos...',
                              style: GalleryTextStyles.bodyMedium.copyWith(
                                color: GalleryColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      );
                    } else if (snapshot.hasError) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: GalleryColors.error,
                              size: 15.0.wp,
                            ),
                            SizedBox(height: 2.0.hp),
                            Text(
                              'Failed to load photos',
                              style: GalleryTextStyles.headlineSmall.copyWith(
                                color: GalleryColors.textPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            SizedBox(height: 1.0.hp),
                            Text(
                              'Please check your connection and try again',
                              style: GalleryTextStyles.bodyMedium.copyWith(
                                color: GalleryColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      );
                    } else {
                      List<dynamic> data = snapshot.data!;
                      if (data.isEmpty) {
                        return Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.photo_library_outlined,
                                color: GalleryColors.textHint,
                                size: 15.0.wp,
                              ),
                              SizedBox(height: 2.0.hp),
                              Text(
                                'No photos found',
                                style: GalleryTextStyles.headlineSmall.copyWith(
                                  color: GalleryColors.textPrimary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(height: 1.0.hp),
                              Text(
                                'This album doesn\'t contain any photos yet',
                                style: GalleryTextStyles.bodyMedium.copyWith(
                                  color: GalleryColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        );
                      }

                      return Container(
                        padding: EdgeInsets.symmetric(horizontal: 4.0.wp),
                        child: GridView.builder(
                          padding: EdgeInsets.only(bottom: 2.0.hp),
                          itemCount: data.length,
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 2,
                                crossAxisSpacing: 3.0.wp,
                                mainAxisSpacing: 3.0.wp,
                                childAspectRatio: 1.0,
                              ),
                          itemBuilder: (context, index) {
                            final item = data[index];
                            return GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => GalleryView(
                                          imagesData: data,
                                          initialIndex: index,
                                        ),
                                  ),
                                );
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: GalleryColors.cardShadow,
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: Stack(
                                    children: [
                                      Image.network(
                                        item['image_path'],
                                        fit: BoxFit.cover,
                                        width: double.infinity,
                                        height: double.infinity,
                                        loadingBuilder: (
                                          context,
                                          child,
                                          loadingProgress,
                                        ) {
                                          if (loadingProgress == null)
                                            return child;
                                          return Container(
                                            color: GalleryColors.surfaceVariant,
                                            child: Center(
                                              child: CircularProgressIndicator(
                                                color: GalleryColors.primary,
                                                value:
                                                    loadingProgress
                                                                .expectedTotalBytes !=
                                                            null
                                                        ? loadingProgress
                                                                .cumulativeBytesLoaded /
                                                            loadingProgress
                                                                .expectedTotalBytes!
                                                        : null,
                                              ),
                                            ),
                                          );
                                        },
                                        errorBuilder: (
                                          context,
                                          error,
                                          stackTrace,
                                        ) {
                                          return Container(
                                            color: GalleryColors.surfaceVariant,
                                            child: Center(
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons.broken_image,
                                                    color:
                                                        GalleryColors.textHint,
                                                    size: 8.0.wp,
                                                  ),
                                                  SizedBox(height: 1.0.hp),
                                                  Text(
                                                    'Failed to load',
                                                    style: GalleryTextStyles
                                                        .bodySmall
                                                        .copyWith(
                                                          color:
                                                              GalleryColors
                                                                  .textHint,
                                                        ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                      // Overlay for better tap indication
                                      Container(
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                          gradient: LinearGradient(
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter,
                                            colors: [
                                              Colors.transparent,
                                              Colors.black.withValues(
                                                alpha: 0.1,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    }
                  },
                ),
              ),
            ],
          ),
          bottomNavigationBar: Container(
            height: MediaQuery.of(context).padding.bottom,
            color: GalleryColors.background,
          ),
        ),
      ),
    );
  }

  Widget popup(String image) {
    return GestureDetector(
      onLongPress: () {
        save_image(image);
        showDownloadMessage(context);
      },
      child: Hero(
        tag: 'fullScreenImage',
        child: Image.network(image, fit: BoxFit.contain),
      ),
    );
  }

  void showSnackBar(BuildContext context) {
    final snackBar = SnackBar(content: Text('Download button pressed!'));

    // Use the ScaffoldMessenger to show the SnackBar
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  void showDownloadMessage(BuildContext context) {
    OverlayEntry overlayEntry;
    overlayEntry = OverlayEntry(
      builder:
          (context) => Positioned(
            bottom: MediaQuery.of(context).size.height * 0.1,
            // left: MediaQuery.of(context).size.width * 0.25,
            left: 0.0,
            right: 0.0,
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding: EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Center(
                  child: Text(
                    'Image Downloaded!',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ),
          ),
    );

    Overlay.of(context)?.insert(overlayEntry);

    // Delayed removal of the overlay
    Future.delayed(Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }

  save_image(image) async {
    // var response = await Dio()
    //     .get(image, options: Options(responseType: ResponseType.bytes));
    // final result = await ImageGallerySaver.saveImage(
    //     Uint8List.fromList(response.data),
    //     quality: 60,
    //     name: "gallery image");

    var response = await Dio().get(
      image,
      options: Options(responseType: ResponseType.bytes),
    );
    final result = await ImageGallerySaverPlus.saveImage(
      Uint8List.fromList(response.data),
      quality: 60,
      name: "gallery image",
    );
    print(result);
  }
}

class ZoomableImageScreen extends StatelessWidget {
  final String image;

  ZoomableImageScreen({required this.image});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: GalleryColors.background,
      appBar: PreferredSize(
        preferredSize: Size(double.infinity, 9.0.hp),
        child: Container(
          decoration: const BoxDecoration(
            gradient: GalleryColors.primaryGradient,
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(
                Icons.arrow_back_ios,
                color: GalleryColors.textOnPrimary,
                size: 24,
              ),
            ),
            title: Text(
              'Photo View',
              style: GalleryTextStyles.headlineSmall.copyWith(
                color: GalleryColors.textOnPrimary,
                fontWeight: FontWeight.w700,
                fontSize: 22,
              ),
            ),
            centerTitle: true,
            actions: [
              IconButton(
                onPressed: () {
                  // Add download functionality if needed
                },
                icon: const Icon(
                  Icons.download,
                  color: GalleryColors.textOnPrimary,
                  size: 24,
                ),
              ),
            ],
          ),
        ),
      ),
      body: Container(
        constraints: BoxConstraints.expand(
          height: MediaQuery.of(context).size.height,
        ),
        child: PhotoView(
          backgroundDecoration: BoxDecoration(color: GalleryColors.background),
          imageProvider: NetworkImage(image),
          minScale: PhotoViewComputedScale.contained * 0.8,
          maxScale: PhotoViewComputedScale.covered * 2,
          enableRotation: true,
          loadingBuilder: (context, event) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: GalleryColors.primary,
                    value:
                        event == null
                            ? null
                            : event.cumulativeBytesLoaded /
                                (event.expectedTotalBytes ?? 1),
                  ),
                  SizedBox(height: 2.0.hp),
                  Text(
                    'Loading image...',
                    style: GalleryTextStyles.bodyMedium.copyWith(
                      color: GalleryColors.textSecondary,
                    ),
                  ),
                ],
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.broken_image,
                    color: GalleryColors.error,
                    size: 15.0.wp,
                  ),
                  SizedBox(height: 2.0.hp),
                  Text(
                    'Failed to load image',
                    style: GalleryTextStyles.headlineSmall.copyWith(
                      color: GalleryColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 1.0.hp),
                  Text(
                    'Please check your connection and try again',
                    style: GalleryTextStyles.bodyMedium.copyWith(
                      color: GalleryColors.textSecondary,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

class GalleryView extends StatefulWidget {
  final List<dynamic> imagesData;
  final int initialIndex;

  const GalleryView({
    Key? key,
    required this.imagesData,
    required this.initialIndex,
  }) : super(key: key);

  @override
  _GalleryViewState createState() => _GalleryViewState();
}

class _GalleryViewState extends State<GalleryView> {
  late PageController _pageController;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _currentPage = widget.initialIndex;
    _pageController = PageController(initialPage: _currentPage);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: GalleryColors.background,
      appBar: PreferredSize(
        preferredSize: Size(double.infinity, 9.0.hp),
        child: Container(
          decoration: const BoxDecoration(
            gradient: GalleryColors.primaryGradient,
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(
                Icons.arrow_back_ios,
                color: GalleryColors.textOnPrimary,
                size: 24,
              ),
            ),
            title: Text(
              'Photo ${_currentPage + 1} of ${widget.imagesData.length}',
              style: GalleryTextStyles.headlineSmall.copyWith(
                color: GalleryColors.textOnPrimary,
                fontWeight: FontWeight.w700,
                fontSize: 22,
              ),
            ),
            centerTitle: true,
            actions: [
              IconButton(
                onPressed: () {
                  if (_currentPage < widget.imagesData.length) {
                    save_image(widget.imagesData[_currentPage]['image_path']);
                    showDownloadMessage(context);
                  }
                },
                icon: const Icon(
                  Icons.download,
                  color: GalleryColors.textOnPrimary,
                  size: 24,
                ),
              ),
            ],
          ),
        ),
      ),
      body: Container(
        color: GalleryColors.background,
        child: PageView.builder(
          controller: _pageController,
          itemCount: widget.imagesData.length,
          onPageChanged: (index) {
            setState(() {
              _currentPage = index;
            });
          },
          itemBuilder: (context, index) {
            return popup(widget.imagesData[index]['image_path'], index);
          },
        ),
      ),
    );
  }

  Widget popup(String image, index) {
    return GestureDetector(
      onLongPress: () {
        save_image(image);
        showDownloadMessage(context);
      },
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ZoomableImageScreen(image: image),
          ),
        );
      },
      child: Container(
        color: Colors.white,
        constraints: BoxConstraints.expand(
          height: MediaQuery.of(context).size.height,
        ),
        child: Hero(
          tag: index,
          child: PhotoView(
            backgroundDecoration: BoxDecoration(
              color: Colors.white, // Ensure the PhotoView background is white
            ),
            imageProvider: NetworkImage(image),
            minScale: PhotoViewComputedScale.contained * 0.8,
            maxScale: PhotoViewComputedScale.covered * 2,
            enableRotation: true,
          ),
        ),

        // Image.network(
        //   image,
        //   fit: BoxFit.contain,
        // ),
      ),
    );
  }

  void showDownloadMessage(BuildContext context) {
    OverlayEntry overlayEntry;
    overlayEntry = OverlayEntry(
      builder:
          (context) => Positioned(
            bottom: MediaQuery.of(context).size.height * 0.1,
            // left: MediaQuery.of(context).size.width * 0.25,
            left: 0.0,
            right: 0.0,
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding: EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Center(
                  child: Text(
                    'Image Downloaded!',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ),
          ),
    );

    Overlay.of(context)?.insert(overlayEntry);

    // Delayed removal of the overlay
    Future.delayed(Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }

  save_image(image) async {
    // var response = await Dio()
    //     .get(image, options: Options(responseType: ResponseType.bytes));
    // final result = await ImageGallerySaver.saveImage(
    //     Uint8List.fromList(response.data),
    //     quality: 60,
    //     name: "gallery image");

    var response = await Dio().get(
      image,
      options: Options(responseType: ResponseType.bytes),
    );
    final result = await ImageGallerySaverPlus.saveImage(
      Uint8List.fromList(response.data),
      quality: 60,
      name: "gallery image",
    );
    print(result);
  }
}
