import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/view/dairy/dairymainscreen.dart';
import 'package:silverleaf/view/dashboard/dashboardscreen.dart';
import 'package:silverleaf/view/gallery/gallerymainscreen.dart';
import 'package:silverleaf/view/message/messagescreen.dart';
import 'dart:io';

int globalBottomBarIndex = 0;
const List pages = [
  DashBoardScreen(),
  GalleryMainScreen(),
  DairyMainScreen(),
  MessageMainScreen(),
];

class MainBoard extends StatefulWidget {
  const MainBoard({super.key});

  @override
  State<MainBoard> createState() => _MainBoardState();
}

class _MainBoardState extends State<MainBoard> {
  String name = '';
  //var globalBottomBarIndex = 0;
  @override
  Widget build(BuildContext context) {
    switch (globalBottomBarIndex) {
      case 0:
        name = "Home";
        break;
      case 1:
        name = "Gallery";
        break;
      case 2:
        name = "Diary";
        break;
      default:
        name = 'Message';
    }

    Future<bool> subback() async {
      if (name != "Home") {
        setState(() {
          globalBottomBarIndex = 0;
        });
        return Future.value(true);
      } else {
        exit(0);
        // return Future.value(false);
      }
    }

    // Set status bar styling to match ProfileSection gradient
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: AppColors.primary, // Match the gradient start color
        statusBarIconBrightness:
            Brightness.light, // Light icons for dark status bar
        statusBarBrightness: Brightness.dark, // For iOS
      ),
    );

    return PopScope(
      canPop:
          name == "Home"
              ? false
              : true, // Disable back navigation on Home screen
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop && name != "Home") {
          await subback();
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar:
            name == "Home"
                ? null
                : PreferredSize(
                  preferredSize: Size(double.infinity, 9.0.hp),
                  child: Container(
                    decoration: const BoxDecoration(
                      gradient: AppColors.primaryGradient,
                    ),
                    child: SafeArea(
                      bottom: false,
                      child: AppBar(
                        backgroundColor: Colors.transparent,
                        elevation: 0,
                        leading: IconButton(
                          onPressed: subback,
                          icon: const Icon(
                            Icons.arrow_back_ios,
                            color: AppColors.textOnPrimary,
                            size: 24,
                          ),
                        ),
                        title: Text(
                          name,
                          style: AppTextStyles.headlineSmall.copyWith(
                            color: AppColors.textOnPrimary,
                            fontWeight: FontWeight.w700,
                            fontSize: 22,
                          ),
                        ),
                        centerTitle: true,
                      ),
                    ),
                  ),
                ),
        // bottomNavigationBar: BottomNavigationBar(
        //     backgroundColor: Colors.white,
        //     elevation: 8,
        //     type: BottomNavigationBarType.fixed,
        //     currentIndex: globalBottomBarIndex,
        //     iconSize: 18.0.sp,
        //     selectedFontSize: 12.0.sp,
        //     unselectedFontSize: 10.0.sp,
        //     selectedItemColor: appcolor,
        //     unselectedItemColor: Colors.black,
        //     onTap: (val) {
        //       setState(() {
        //         globalBottomBarIndex = val;
        //         dairypages = 0;
        //         gallerypages = 0;
        //         messagescreenpages = 0;
        //       });
        //     },
        //     items: [
        //       BottomNavigationBarItem(
        //           icon: SizedBox(
        //             height: 3.8.hp,
        //             width: 3.5.hp,
        //             child: Image.asset('images/home_Vector.png'),
        //           ),
        //           label: "Home"),
        //       BottomNavigationBarItem(
        //           icon: SizedBox(
        //             height: 3.8.hp,
        //             width: 3.5.hp,
        //             child: Image.asset('images/galleryvector.png'),
        //           ),
        //           label: "Galllery"),
        //       BottomNavigationBarItem(
        //           icon: SizedBox(
        //             height: 3.8.hp,
        //             width: 3.5.hp,
        //             child: Image.asset('images/dairyvector.png'),
        //           ),
        //           label: "Dairy"),
        //       BottomNavigationBarItem(
        //           icon: SizedBox(
        //             height: 3.8.hp,
        //             width: 3.5.hp,
        //             child: Image.asset('images/chatevector.png'),
        //           ),
        //           label: "message"),
        //     ]),
        body: pages[globalBottomBarIndex],
        bottomNavigationBar: Container(
          height: MediaQuery.of(context).padding.bottom,
          color: AppColors.background, // Match the body color
        ),
      ),
    );
  }
}
