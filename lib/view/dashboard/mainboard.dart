import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/dairy/dairymainscreen.dart';
import 'package:silverleaf/view/dashboard/dashboardscreen.dart';
import 'package:silverleaf/view/gallery/gallerymainscreen.dart';
import 'package:silverleaf/view/message/messagescreen.dart';
import 'package:silverleaf/widgets/chatWidgets.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:io';

int globalBottomBarIndex = 0;
const List pages = [
  DashBoardScreen(),
  GalleryMainScreen(),
  DairyMainScreen(),
  MessageMainScreen(),
];

class MainBoard extends StatefulWidget {
  const MainBoard({super.key});

  @override
  State<MainBoard> createState() => _MainBoardState();
}

class _MainBoardState extends State<MainBoard> {
  String name = '';
  //var globalBottomBarIndex = 0;
  @override
  Widget build(BuildContext context) {
    switch (globalBottomBarIndex) {
      case 0:
        name = "Home";
        break;
      case 1:
        name = "Gallery";
        break;
      case 2:
        name = "Diary";
        break;
      default:
        name = 'Message';
    }

    void dispose() {
      print('main board');
      super.dispose();
    }

    Future<bool> subback() async {
      if (name != "Home") {
        //  await Get.offAll(() => MainBoard());

        globalBottomBarIndex = 0;
        // });

        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => MainBoard()),
        );

        return Future.value(true);
      } else {
        exit(0);
        // return Future.value(false);
      }
    }

    CustomizedAppBar appbar = CustomizedAppBar(
      back: () {
        print('gggggggg');
        setState(() {
          globalBottomBarIndex = 0;
          Get.forceAppUpdate();
        });
      },
      profile: () {},
      screenName: name,
      screen_id: 2,
    );
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: appcolor, // Match the app bar green color
        statusBarIconBrightness:
            Brightness.light, // Light icons for dark status bar
        statusBarBrightness: Brightness.dark, // For iOS
      ),
    );

    return WillPopScope(
      onWillPop: subback,
      child: SafeArea(
        child: Scaffold(
          appBar: PreferredSize(
            preferredSize: Size(double.infinity, 9.0.hp),
            child: CustomizedAppBar(
              back: subback,
              profile: () {},
              screenName: name,
              screen_id: 2,
            ),
          ),
          // bottomNavigationBar: BottomNavigationBar(
          //     backgroundColor: Colors.white,
          //     elevation: 8,
          //     type: BottomNavigationBarType.fixed,
          //     currentIndex: globalBottomBarIndex,
          //     iconSize: 18.0.sp,
          //     selectedFontSize: 12.0.sp,
          //     unselectedFontSize: 10.0.sp,
          //     selectedItemColor: appcolor,
          //     unselectedItemColor: Colors.black,
          //     onTap: (val) {
          //       setState(() {
          //         globalBottomBarIndex = val;
          //         dairypages = 0;
          //         gallerypages = 0;
          //         messagescreenpages = 0;
          //       });
          //     },
          //     items: [
          //       BottomNavigationBarItem(
          //           icon: SizedBox(
          //             height: 3.8.hp,
          //             width: 3.5.hp,
          //             child: Image.asset('images/home_Vector.png'),
          //           ),
          //           label: "Home"),
          //       BottomNavigationBarItem(
          //           icon: SizedBox(
          //             height: 3.8.hp,
          //             width: 3.5.hp,
          //             child: Image.asset('images/galleryvector.png'),
          //           ),
          //           label: "Galllery"),
          //       BottomNavigationBarItem(
          //           icon: SizedBox(
          //             height: 3.8.hp,
          //             width: 3.5.hp,
          //             child: Image.asset('images/dairyvector.png'),
          //           ),
          //           label: "Dairy"),
          //       BottomNavigationBarItem(
          //           icon: SizedBox(
          //             height: 3.8.hp,
          //             width: 3.5.hp,
          //             child: Image.asset('images/chatevector.png'),
          //           ),
          //           label: "message"),
          //     ]),
          backgroundColor: Colors.white,
          // body: pages[globalBottomBarIndex],
          body: DashBoardScreen(),
          bottomNavigationBar: Container(
            height: MediaQuery.of(context).padding.bottom,
            color: Colors.white, // Match the body color
          ),
        ),
      ),
    );
  }
}
