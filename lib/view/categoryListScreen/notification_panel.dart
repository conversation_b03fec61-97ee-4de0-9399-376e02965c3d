// ignore_for_file: avoid_unnecessary_containers, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';

import 'package:http/http.dart' as http;
import 'dart:convert';

import 'package:silverleaf/view/dashboard/mainboard.dart';
import 'package:url_launcher/url_launcher.dart';

class NotificationPanel extends StatefulWidget {
  const NotificationPanel({super.key});

  @override
  State<NotificationPanel> createState() => _NotificationPanelState();
}

class _NotificationPanelState extends State<NotificationPanel> {
  List<dynamic>? jsonData;
  List userNames = [];
  bool isLoading = true;
  String? errorMessage;

  Future<List<dynamic>> fetchnotificationData() async {
    if (userNames.isEmpty || userNames.length < 8) {
      throw Exception('User data not available');
    }

    final url =
        'https://silverleafms.in/silvar_leaf/api/notification/view-notification/${userNames[6]}/${userNames[7]}';

    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final responseBody = json.decode(response.body);
        if (responseBody['data'] != null) {
          return List<dynamic>.from(responseBody['data']);
        } else {
          return [];
        }
      } else {
        throw Exception('Failed to load notifications: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames.isNotEmpty) {
      setState(() {
        userNames = storedUserNames;
      });
    }
  }

  void back() {
    Navigator.pop(context);
  }

  @override
  void initState() {
    super.initState();
    getListString();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Color(0xFFF8F9FA),
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [appcolor, appcolor.withValues(alpha: 0.8)],
              ),
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: IconButton(
                onPressed: back,
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              title: Text(
                'Notifications',
                style: textStyle.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                  fontSize: 22,
                ),
              ),
              centerTitle: true,
            ),
          ),
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xFFFAFAFA), Color(0xFFFFFFFF)],
            ),
          ),
          child:
              userNames.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(color: appcolor),
                        SizedBox(height: 2.0.hp),
                        Text(
                          'Loading user data...',
                          style: textStyle.copyWith(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  )
                  : FutureBuilder<List<dynamic>>(
                    future: fetchnotificationData(),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(color: appcolor),
                              SizedBox(height: 2.0.hp),
                              Text(
                                'Loading notifications...',
                                style: textStyle.copyWith(
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        );
                      } else if (snapshot.hasError) {
                        return Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.error_outline,
                                size: 15.0.wp,
                                color: Colors.red[400],
                              ),
                              SizedBox(height: 2.0.hp),
                              Text(
                                'Failed to load notifications',
                                style: textStyle.copyWith(
                                  color: Colors.red[600],
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(height: 1.0.hp),
                              Text(
                                '${snapshot.error}',
                                style: textStyle.copyWith(
                                  color: Colors.grey[600],
                                  fontSize: 10.0.sp,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              SizedBox(height: 2.0.hp),
                              ElevatedButton(
                                onPressed: () {
                                  setState(() {});
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: appcolor,
                                  foregroundColor: Colors.white,
                                ),
                                child: Text('Retry'),
                              ),
                            ],
                          ),
                        );
                      } else {
                        List<dynamic> data = snapshot.data!;
                        if (data.isEmpty) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.notifications_none,
                                  size: 15.0.wp,
                                  color: Colors.grey[400],
                                ),
                                SizedBox(height: 2.0.hp),
                                Text(
                                  'No notifications yet',
                                  style: textStyle.copyWith(
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                SizedBox(height: 1.0.hp),
                                Text(
                                  'You\'ll see notifications here when they arrive',
                                  style: textStyle.copyWith(
                                    color: Colors.grey[500],
                                    fontSize: 10.0.sp,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          );
                        } else {
                          return ListView.builder(
                            padding: EdgeInsets.all(4.0.wp),
                            itemCount: snapshot.data!.length,
                            itemBuilder: (context, index) {
                              final item = data[index];

                              // Parse the input date string into a DateTime object
                              DateTime inputDate = DateTime.parse(
                                item['created_at'],
                              );

                              // Format the DateTime object into "MMM dd yyyy" format
                              String formattedDate = DateFormat(
                                'MMM dd yyyy',
                              ).format(inputDate);

                              // Get notification type icon and color
                              IconData notificationIcon = Icons.notifications;
                              Color notificationColor = appcolor;

                              if (item["annocement_name"]
                                  .toString()
                                  .toLowerCase()
                                  .contains('leave')) {
                                notificationIcon = Icons.event_busy;
                                notificationColor = Colors.orange;
                              } else if (item["annocement_name"]
                                  .toString()
                                  .toLowerCase()
                                  .contains('exam')) {
                                notificationIcon = Icons.quiz;
                                notificationColor = Colors.blue;
                              } else if (item["annocement_name"]
                                  .toString()
                                  .toLowerCase()
                                  .contains('event')) {
                                notificationIcon = Icons.event;
                                notificationColor = Colors.purple;
                              }

                              return Container(
                                margin: EdgeInsets.only(bottom: 2.0.hp),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withOpacity(0.1),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(12),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder:
                                              (context) =>
                                                  NotificationSecondScreenDetails(
                                                    title:
                                                        item['annocement_name'],
                                                    description:
                                                        item['description'],
                                                    url: item['url'] ?? '',
                                                    user_id: '0',
                                                  ),
                                        ),
                                      );
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.all(4.0.wp),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // Header with icon and title
                                          Row(
                                            children: [
                                              Container(
                                                padding: EdgeInsets.all(2.0.wp),
                                                decoration: BoxDecoration(
                                                  color: notificationColor
                                                      .withOpacity(0.1),
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                                child: Icon(
                                                  notificationIcon,
                                                  color: notificationColor,
                                                  size: 5.0.wp,
                                                ),
                                              ),
                                              SizedBox(width: 3.0.wp),
                                              Expanded(
                                                child: Text(
                                                  item["annocement_name"]
                                                      .toString(),
                                                  style: textStyle.copyWith(
                                                    fontWeight: FontWeight.w700,
                                                    fontSize: 14.0.sp,
                                                    color: Colors.grey[800],
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                padding: EdgeInsets.symmetric(
                                                  horizontal: 2.0.wp,
                                                  vertical: 0.5.hp,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: appcolor.withOpacity(
                                                    0.1,
                                                  ),
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                child: Text(
                                                  formattedDate,
                                                  style: textStyle.copyWith(
                                                    fontSize: 8.0.sp,
                                                    color: appcolor,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),

                                          // Leave date range for leave notifications
                                          if (item["annocement_name"]
                                                  .toString()
                                                  .toLowerCase()
                                                  .contains('leave') &&
                                              item['date'] != null &&
                                              item['to_date'] != null) ...[
                                            SizedBox(height: 1.5.hp),
                                            Container(
                                              padding: EdgeInsets.symmetric(
                                                horizontal: 3.0.wp,
                                                vertical: 1.0.hp,
                                              ),
                                              decoration: BoxDecoration(
                                                color: Colors.orange
                                                    .withOpacity(0.1),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                border: Border.all(
                                                  color: Colors.orange
                                                      .withOpacity(0.3),
                                                  width: 1,
                                                ),
                                              ),
                                              child: Row(
                                                children: [
                                                  Icon(
                                                    Icons.date_range,
                                                    color: Colors.orange,
                                                    size: 4.0.wp,
                                                  ),
                                                  SizedBox(width: 2.0.wp),
                                                  Text(
                                                    '${item['date'].toString()} to ${item['to_date'].toString()}',
                                                    style: textStyle.copyWith(
                                                      color: Colors.orange[700],
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      fontSize: 10.0.sp,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],

                                          SizedBox(height: 2.0.hp),

                                          // Description
                                          Text(
                                            item['description'].toString(),
                                            maxLines: 3,
                                            overflow: TextOverflow.ellipsis,
                                            style: textStyle.copyWith(
                                              fontWeight: FontWeight.w400,
                                              fontSize: 11.0.sp,
                                              color: Colors.grey[700],
                                              height: 1.4,
                                            ),
                                          ),

                                          SizedBox(height: 2.0.hp),

                                          // Read more button
                                          Align(
                                            alignment: Alignment.centerRight,
                                            child: Container(
                                              padding: EdgeInsets.symmetric(
                                                horizontal: 3.0.wp,
                                                vertical: 1.0.hp,
                                              ),
                                              decoration: BoxDecoration(
                                                gradient: LinearGradient(
                                                  colors: [
                                                    appcolor,
                                                    appcolor.withOpacity(0.8),
                                                  ],
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                              ),
                                              child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Text(
                                                    'Read More',
                                                    style: textStyle.copyWith(
                                                      color: Colors.white,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      fontSize: 9.0.sp,
                                                    ),
                                                  ),
                                                  SizedBox(width: 1.0.wp),
                                                  Icon(
                                                    Icons.arrow_forward_ios,
                                                    color: Colors.white,
                                                    size: 3.0.wp,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          );
                        }
                      }
                    },
                  ),
        ),
      ),
    );
  }
}

class NotificationSecondScreenDetails extends StatefulWidget {
  final String title;
  final String description;

  final String user_id;

  final String url;

  const NotificationSecondScreenDetails({
    super.key,
    required this.title,
    required this.description,
    required this.url,
    required this.user_id,
  });

  @override
  State<NotificationSecondScreenDetails> createState() => NotificationDetails();
}

class NotificationDetails extends State<NotificationSecondScreenDetails> {
  subback() {
    if (widget.user_id == '0') {
      Navigator.pop(context, true);
    } else {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => MainBoard()),
      );
    }

    // Get.to(const MainBoard());

    print('back button widget clicked diary');
    //Get.forceAppUpdate();
  }

  void initState() {
    if (widget.user_id != '0') {
      fetchstudent(widget.user_id);
    }
    super.initState();
  }

  fetchstudent(id) async {
    int currentYear = DateTime.now().year;
    int month = DateTime.now().month;

    final url = 'https://silverleafms.in/silvar_leaf/api/students/auth-account';
    final data = {'id': id};

    final response = await http.post(Uri.parse(url), body: data);
    if (response.statusCode == 200) {
      final result = json.decode(response.body);

      List<Map<String, dynamic>> jsonData = List<Map<String, dynamic>>.from(
        result['data'],
      );

      List<dynamic> dataList = result['data'];

      List<dynamic> filteredData =
          dataList.where((item) {
            if ((int.parse(item['academic_year'].toString().split('-')[0]) ==
                    currentYear) &&
                month >= 5) {
              return true;
            } else if ((int.parse(
                      item['academic_year'].toString().split('-')[1],
                    ) ==
                    currentYear) &&
                month <= 4) {
              return true;
            } else {
              return false;
            }
          }).toList();

      print(filteredData);

      if (filteredData.length > 0) {
        filteredData
            .map(
              (result) => setListString(
                id: result['id'].toString(),
                name: result['name'],
                class_name: result['class_name'],
                branch_name: result['branch_name'],
                section_name: result['section_name'],
                section_id: result['section_id'].toString(),
                class_id: result['class_id'].toString(),
                branch_id: result['branch_id'].toString(),
                type: result['type'].toString(),
                profile: result['profile'],
                bloodgroup: result['blood_group_name'],
                fathername: result['father_name'],
                mothername: result['mother_name'],
                primarynumber: result['primary_number'].toString(),
                secondarynumber: result['secondary_number'].toString(),
                year_id: result['academic_id'].toString(),
                status_type: '1',
              ),
            )
            .toList();
      }
      setState(() {});
      // print(student_data_profile.length);

      //return result['data'];
    } else {
      print('tttttttttttttttttttttelse');
      throw Exception('Failed to load data');
    }
  }

  Future setListString({
    required String id,
    required String name,
    required String class_name,
    required String branch_name,
    required String section_name,
    required String section_id,
    required String class_id,
    required String branch_id,
    required String type,
    required String profile,
    required String bloodgroup,
    required String fathername,
    required String mothername,
    required String primarynumber,
    required String secondarynumber,
    required String year_id,
    required String status_type,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.remove('users');

    prefs.setStringList('users', [
      id,
      name,
      class_name,
      branch_name,
      section_name,
      section_id,
      class_id,
      branch_id,
      profile,
      bloodgroup,
      fathername,
      mothername,
      primarynumber,
      secondarynumber,
      year_id,
      type,
    ]);
  }

  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      throw 'Could not launch $url';
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Color(0xFFF8F9FA),
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [appcolor, appcolor.withValues(alpha: 0.8)],
              ),
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: IconButton(
                onPressed: () {
                  subback();
                },
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              title: Text(
                widget.title,
                style: textStyle.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                  fontSize: 22,
                ),
              ),
              centerTitle: true,
            ),
          ),
        ),
        body: WillPopScope(
          onWillPop: () => subback(),
          child: Column(
            children: [
              SizedBox(height: 2.0.hp),
              Expanded(
                child: Container(
                  child: Padding(
                    padding: EdgeInsets.all(17.0.sp),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.description,
                          style: dairyTextStyle.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (widget.url != '') ...[
                          GestureDetector(
                            onTap: () {
                              _launchUrl(widget.url);
                            },
                            child: Text(
                              widget.url,
                              style: dairyTextStyle.copyWith(
                                fontWeight: FontWeight.w600,
                                color: Colors.blue,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
