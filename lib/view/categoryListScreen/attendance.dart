// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/main.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:silverleaf/utils.dart';

// Professional design constants for Attendance
class AttendanceColors {
  static const Color primary = Color(0xFF2E7D32);
  static const Color primaryLight = Color(0xFF4CAF50);
  static const Color secondary = Color(0xFF1976D2);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color background = Color(0xFFF8F9FA);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFF9E9E9E);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color divider = Color(0xFFE0E0E0);
  static const Color cardShadow = Color(0x1A000000);
  static const Color success = Color(0xFF4CAF50);
  static const Color error = Color(0xFFE53935);
  static const Color warning = Color(0xFFFF9800);

  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFF2E7D32), Color(0xFF4CAF50)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient successGradient = LinearGradient(
    colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient errorGradient = LinearGradient(
    colors: [Color(0xFFE53935), Color(0xFFEF5350)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}

class AttendanceTextStyles {
  static const TextStyle headlineLarge = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w700,
    color: AttendanceColors.textPrimary,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: AttendanceColors.textPrimary,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: AttendanceColors.textPrimary,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: AttendanceColors.textPrimary,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: AttendanceColors.textSecondary,
  );
}

class Attendance extends StatefulWidget {
  const Attendance({super.key});

  @override
  State<Attendance> createState() => _AttendanceState();
}

class _AttendanceState extends State<Attendance> {
  @override
  // CalendarController _controller;
  //  void initState() {
  //     super.initState();
  //     _controller = CalendarController();
  //   }
  Widget build(BuildContext context) {
    return SafeArea(
      child:
          isTeacher == true
              ? TeacherAttendanceScreen()
              : StudentsAttendanceScreen(),
    );
  }
}

class TeacherAttendanceScreen extends StatefulWidget {
  const TeacherAttendanceScreen({super.key});

  @override
  State<TeacherAttendanceScreen> createState() =>
      _TeacherAttendanceScreenState();
}

class _TeacherAttendanceScreenState extends State<TeacherAttendanceScreen> {
  List userNames = [];
  var jsonData;
  var student_leave_type;
  var status;

  SubmitStudent student = new SubmitStudent();
  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      print(storedUserNames);

      setState(() {
        userNames = storedUserNames;
        this.list_student();
      });
    }
  }

  void initState() {
    super.initState();
    this.getListString();
  }

  Future<List> list_student() async {
    final url =
        'https://silverleafms.in/silvar_leaf/api/attendance/get-students';
    final data = {
      'class_id': userNames[6],
      'section_id': userNames[5],
      'teacher_id': userNames[0],
    };
    print(data);
    final response = await http.post(Uri.parse(url), body: data);

    if (response.statusCode == 200) {
      print(response.body);
      var responseBody = json.decode(response.body);
      var response_student = responseBody['data'];
      List<Map<String, dynamic>> jsonData = List<Map<String, dynamic>>.from(
        responseBody['data'],
      );
      return response_student;
    } else {
      throw Exception('Failed to load data');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: Size(double.infinity, 9.0.hp),
        child: CustomizedAppBar(
          back: back,
          profile: () {},
          screenName: 'Attendance',
          screen_id: 2,
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ///header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              topic(title: "Sl.no"),
              topic(title: "Name"),
              topic(title: " "),
            ],
          ),
          SizedBox(height: 2.0.hp),
          Expanded(
            child: SizedBox(
              child: FutureBuilder<List<dynamic>>(
                future: list_student(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError) {
                    return Center(child: CircularProgressIndicator());
                  } else {
                    if (snapshot.data!.length == 0) {
                      return Center(child: Text('No Data'));
                    } else {
                      List<dynamic> data = snapshot.data!;
                      return ListView.separated(
                        separatorBuilder: (context, index) {
                          return SizedBox(height: 2.0.hp);
                        },
                        itemCount: snapshot.data!.length,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          final item = data[index];
                          return listOfStudents(
                            index: index,
                            topic: item['name'],
                            type: item['status_count'],
                            id: item['id'],
                          );
                        },
                      );
                    }
                  }
                },
              ),
            ),
          ),
          SizedBox(height: 3.0.hp),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Align(
              alignment: Alignment.centerRight,
              child: GestureDetector(
                onTap: () async {
                  List<Map<String, dynamic>> respond_student = await student
                      .save_attendance(
                        userNames[6],
                        userNames[5],
                        userNames[0],
                        this.jsonData,
                      );
                  // print(respond_student[0]['absent_count']);
                  showPopup(
                    context,
                    respond_student[0]['absent_count'],
                    respond_student[0]['status'],
                  );
                },
                child: Container(
                  height: 8.0.hp,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: appcolor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  width: 40.0.wp,
                  child: Text(
                    "Submit Attendance",
                    style: textStyle.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                      fontSize: 10.0.sp,
                    ),
                  ),
                  // onPressed: () {

                  // },
                ),
              ),
            ),
          ),
          SizedBox(height: 3.0.hp),
        ],
      ),
    );
  }

  void showPopup(BuildContext context, absent, status) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.black26,
          surfaceTintColor: Colors.black26,
          title: Text(
            status == 'success' ? '$absent Students' : 'Warning',
            textAlign: TextAlign.center,
            style: textStyle.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w700,
              fontSize: 12.0.sp,
            ),
          ),
          content: Text(
            status == 'success'
                ? 'are on Leave today\nSend Message to their Parents'
                : 'Please try again later',
            style: textStyle.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w700,
              fontSize: 12.0.sp,
            ),
            textAlign: TextAlign.center,
          ),
          actionsAlignment: MainAxisAlignment.center,
          actions: <Widget>[
            GestureDetector(
              onTap: () {
                Navigator.of(context).pop(); // Close the popup
              },
              child: Container(
                height: 6.0.hp,
                width: 18.0.wp,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: appcolor,
                  borderRadius: BorderRadius.circular(7.0.sp),
                ),
                child: Text(
                  'Yes',
                  style: textStyle.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 11.0.sp,
                  ),
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                Navigator.of(context).pop(); // Close the popup
              },
              child: Container(
                height: 6.0.hp,
                width: 18.0.wp,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(7.0.sp),
                ),
                child: Text(
                  'No',
                  style: textStyle.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 11.0.sp,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget listOfStudents({index, topic, type, id}) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Container(
            width: MediaQuery.of(context).size.width / 3,
            alignment: Alignment.center,
            child: Text(
              '${index + 1}',
              style: textStyle.copyWith(
                fontSize: 10.0.sp,
                fontWeight: FontWeight.w800,
              ),
            ),
          ),
          Container(
            width: MediaQuery.of(context).size.width / 3,
            alignment: Alignment.center,
            child: Text(
              '${topic}',
              style: textStyle.copyWith(
                fontSize: 10.0.sp,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
          AttendanceRegisterRadio(
            user_id: id,
            type: type,
            student: this.student,
          ),
        ],
      ),
    );
  }

  Widget topic({title}) {
    return Container(
      height: 5.0.hp,
      color: appcolor.withOpacity(.3),
      width: MediaQuery.of(context).size.width / 3,
      alignment: Alignment.center,
      child: Text(
        title,
        style: textStyle.copyWith(
          fontSize: 12.0.sp,
          fontWeight: FontWeight.w700,
        ),
      ),
    );
  }
}

class AttendanceRegisterRadio extends StatefulWidget {
  final int user_id;
  int type;
  var student;
  AttendanceRegisterRadio({
    super.key,
    required this.user_id,
    required this.type,
    this.student,
  });

  @override
  State<AttendanceRegisterRadio> createState() =>
      _AttendanceRegisterRadioState();
}

class _AttendanceRegisterRadioState extends State<AttendanceRegisterRadio> {
  bool light = false;
  //SubmitStudent student = new SubmitStudent();
  @override
  Widget build(BuildContext context) {
    print('widget${widget.type}');
    if (widget.type == 2) {
      widget.student.push_student(widget.user_id, 2);
      this.light = true;
    } else {
      //student.push_student(widget.user_id, 0);
      widget.student.push_student(widget.user_id, 0);
      this.light = false;
    }

    return Container(
      alignment: Alignment.center,
      width: MediaQuery.of(context).size.width / 3,
      child: Switch(
        value: light,
        inactiveThumbColor: Colors.red,
        activeColor: appcolor,
        onChanged: (bool value) {
          setState(() {
            print(value);
            if (value == true) {
              print('success');
              widget.type = 2;
              widget.student.push_student(widget.user_id, 2);
            } else if (value == false) {
              print('failed');
              widget.type = 0;
              widget.student.pop_student(widget.user_id, 0);
            }
            light = value;
          });
        },
      ),
    );
  }
}

class SubmitStudent {
  List<Map<String, dynamic>> student_id = [];

  push_student(id, status) {
    print('pushid:$id');
    var student_count = countStatus(student_id, id, 'students');

    print(student_count);
    if (student_count == 0) {
      student_id.add({'students': id, 'status': status});
    } else {
      updateStatus(student_id, id, status);
    }

    print(student_id);
  }

  pop_student(id, status) {
    print('popid:$id');
    updateStatus(student_id, id, status);
    print(student_id);
  }

  List<Map<String, dynamic>> removeDuplicates(
    List<Map<String, dynamic>> dataList,
    String key,
  ) {
    Set<dynamic> uniqueValues = {};

    List<Map<String, dynamic>> uniqueList =
        dataList.where((map) {
          final value = map[key];
          if (!uniqueValues.contains(value)) {
            uniqueValues.add(value);
            return true;
          } else {
            return false;
          }
        }).toList();

    return uniqueList;
  }

  Future<List<Map<String, dynamic>>> save_attendance(
    class_id,
    section_id,
    staff_id,
    data,
  ) async {
    print('submitedd successfully');

    List<Map<String, dynamic>> uniqueList = removeDuplicates(
      student_id,
      'students',
    );

    var students = jsonEncode(uniqueList);
    // print(student_id);
    // print(uniqueList);
    final url =
        'https://silverleafms.in/silvar_leaf/api/attendance/post-attendance';

    final response = await http.post(
      Uri.parse(url),
      body: {
        'class_id': class_id,
        'section_id': section_id,
        'staff_id': staff_id,
        'students': students,
      },
    );

    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);
      // student_id = [];
      // uniqueList = [];
      // students = '';
      //print(responseBody['absent']);
      // print(uniqueList);
      //int statusCount = countStatus(uniqueList, 0, 'status');

      // print('Count of status 0: $statusCount');
      return [
        {'status': 'success', 'absent_count': responseBody['absent']},
      ];
    } else {
      return [
        {'status': 'failed'},
      ];
    }
  }

  void updateStatus(
    List<Map<String, dynamic>> dataList,
    int studentsValue,
    int newStatus,
  ) {
    for (int i = 0; i < dataList.length; i++) {
      if (dataList[i]['students'] == studentsValue) {
        dataList[i]['status'] = newStatus;
      }
    }
  }

  int countStatus(List<Map<String, dynamic>> list, int statusToCount, type) {
    int count = 0;

    for (var item in list) {
      if (item['students'] == statusToCount && type == 'students') {
        count++;
      } else if (item['status'] == statusToCount && type == 'status') {
        count++;
      }
    }

    return count;
  }
}

class StudentsAttendanceScreen extends StatefulWidget {
  const StudentsAttendanceScreen({super.key});

  @override
  State<StudentsAttendanceScreen> createState() =>
      _StudentsAttendanceScreenState();
}

class _StudentsAttendanceScreenState extends State<StudentsAttendanceScreen> {
  ScrollController _scrollController = ScrollController();
  List items = [];
  int currentPage = 1;
  bool isLoading = false;
  List userNames = [];
  late final ValueNotifier<List<Event>> _selectedEvents;
  CalendarFormat _calendarFormat = CalendarFormat.month;
  RangeSelectionMode _rangeSelectionMode =
      RangeSelectionMode
          .toggledOff; // Can be toggled on/off by longpressing a date
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  DateTime? _rangeStart;
  DateTime? _rangeEnd;
  late DateTime _selectedDate;
  @override
  void initState() {
    super.initState();
    _selectedDate = DateTime.now();

    print('today date');

    print(DateFormat('dd/MM/yy').format(_selectedDate));
    getListString();
  }

  void dispose() {
    _scrollController.dispose();
    // _selectedEvents.dispose();
    super.dispose();
  }

  void _previousMonth() {
    // Move to the previous month
    setState(() {
      _selectedDate = DateTime(_selectedDate.year, _selectedDate.month - 1, 1);
    });
  }

  void _nextMonth() {
    // Move to the next month
    setState(() {
      _selectedDate = DateTime(_selectedDate.year, _selectedDate.month + 1, 1);
    });
  }

  void _scrollListener() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      // Reached the bottom, load more data
      if (!isLoading) {
        // fetchData();
      } else {
        print('false');
      }
    }
  }

  List<Map<String, dynamic>> jsonData = [];

  List<Event> _getEventsForDay(DateTime day) {
    // Implementation example
    print('event date');
    print(kEvents[day]);
    return kEvents[day] ?? [];
  }

  List<Event> _getEventsForRange(DateTime start, DateTime end) {
    // Implementation example
    final days = daysInRange(start, end);
    //print(days);
    return [for (final d in days) ..._getEventsForDay(d)];
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    if (!isSameDay(_selectedDay, selectedDay)) {
      setState(() {
        _selectedDay = selectedDay;
        _focusedDay = focusedDay;
        _rangeStart = null; // Important to clean those
        _rangeEnd = null;
        _rangeSelectionMode = RangeSelectionMode.toggledOff;
      });

      print(selectedDay);
      _selectedEvents.value = _getEventsForDay(selectedDay);
    }
  }

  Map<String, Map<String, dynamic>> presentRecords = {};
  Map<String, Map<String, dynamic>> absentRecords = {};
  void _onRangeSelected(DateTime? start, DateTime? end, DateTime focusedDay) {
    setState(() {
      _selectedDay = null;
      _focusedDay = focusedDay;
      _rangeStart = start;
      _rangeEnd = end;
      _rangeSelectionMode = RangeSelectionMode.toggledOn;
    });

    // `start` or `end` could be null
    if (start != null && end != null) {
      _selectedEvents.value = _getEventsForRange(start, end);
    } else if (start != null) {
      _selectedEvents.value = _getEventsForDay(start);
    } else if (end != null) {
      _selectedEvents.value = _getEventsForDay(end);
    }
  }

  bool isStudentHoliday(DateTime day) {
    jsonData.map((result) {});
    return day.weekday == DateTime.saturday || day.weekday == DateTime.sunday;
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      userNames = storedUserNames;
      fetchleaveData();
    }
  }

  bool isSunday(DateTime day) {
    return day.weekday == DateTime.sunday;
  }

  Map<String, List<Map<String, dynamic>>> _jsonData = {};
  var presentfilteredRecords = 0;
  var absentfilteredRecords = 0;
  List jsondata = [];
  fetchleaveData() async {
    var url;
    final data;
    print(_selectedDate.month);
    url = 'https://silverleafms.in/silvar_leaf/api/leave/view-details';
    data = {
      'user': userNames[0],
      'month': _selectedDate.month.toString(),
      'year': _selectedDate.year.toString(),
    };

    final response = await http.post(Uri.parse(url), body: data);
    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);

      print('length all api response data');

      final List<Map<String, dynamic>> data = List<Map<String, dynamic>>.from(
        responseBody['data'],
      );
      print('dairy response data');

      setState(() {
        jsondata = data;

        jsondata.forEach((record) {
          if (record['status'] == 2) {
            DateTime recordDate = DateFormat('dd/MM/yy').parse(record['date']);
            if (recordDate.month == _selectedDate.month &&
                recordDate.year == _selectedDate.year) {
              presentRecords[record['date']] = record;
            }
          }
        });

        jsondata.forEach((record) {
          if (record['status'] == 0) {
            DateTime recordDate = DateFormat('dd/MM/yy').parse(record['date']);
            if (recordDate.month == _selectedDate.month &&
                recordDate.year == _selectedDate.year) {
              absentRecords[record['date']] = record;
            }
          }
        });

        presentfilteredRecords = presentRecords.values.toList().length;
        absentfilteredRecords = absentRecords.values.toList().length;
      });

      print(jsondata);

      return jsondata;
    } else {
      throw Exception('Failed to load data');
    }
  }

  int daysInMonth(int month, int year) {
    if (month == 2) {
      if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
        return 29; // February has 29 days in a leap year
      } else {
        return 28;
      }
    } else if (month == 4 || month == 6 || month == 9 || month == 11) {
      return 30;
    } else {
      return 31;
    }
  }

  Widget leave_details() {
    return Padding(
      padding: EdgeInsets.only(top: 10),
      child: Column(
        children: [
          Expanded(
            child: Container(
              child: FutureBuilder<Map<String, List<Map<String, dynamic>>>>(
                future: fetchleaveData(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return Center(child: CircularProgressIndicator());
                  } else if (snapshot.hasError) {
                    return Center(child: CircularProgressIndicator());
                  } else {
                    Map<String, List<Map<String, dynamic>>> groupedData =
                        snapshot.data!;
                    return ListView.separated(
                      separatorBuilder: (context, index) {
                        return const Divider();
                      },
                      itemCount: snapshot.data!.length,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        if (snapshot.data!.length == 0) {
                          return Center(child: Text('No Data'));
                        } else {
                          String dateKey = groupedData.keys.elementAt(index);
                          List<Map<String, dynamic>> dataForDate =
                              groupedData[dateKey]!;
                          int dateIndex = index ~/ 2;
                          bool isLastDate = dateIndex == groupedData.length - 1;

                          print(index);
                          return Container(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: 8.0),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children:
                                      dataForDate.map((item) {
                                        var leave;
                                        if (item['leave_type'].toString() ==
                                            '2') {
                                          leave = 'Sick';
                                        } else if (item['leave_type']
                                                .toString() ==
                                            '3') {
                                          leave = 'Personal';
                                        } else if (item['leave_type']
                                                .toString() ==
                                            '4') {
                                          leave = 'Family Function';
                                        } else if (item['leave_type']
                                                .toString() ==
                                            '5') {
                                          leave = 'Other';
                                        }

                                        return GestureDetector(
                                          onTap: () {
                                            setState(() {});
                                            // Get.to(const DairySubPage());
                                          },
                                          child: Container(
                                            // color: index < 3
                                            //     ? Color(0xffF4FFEE)
                                            //     : Colors.transparent,
                                            color:
                                                index == 0 || index % 2 == 0
                                                    ? Color(0xffF4FFEE)
                                                    : Colors.transparent,
                                            child: Padding(
                                              padding: EdgeInsets.all(17.0.sp),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  SizedBox(
                                                    child: Row(
                                                      children: [
                                                        Text(
                                                          //   "Social Science",
                                                          leave,
                                                          style: dairyTextStyle
                                                              .copyWith(
                                                                color:
                                                                    subappcolor,
                                                                fontSize: 18,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w700,
                                                              ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  SizedBox(height: 10),
                                                  Text(
                                                    item['description'],
                                                    style: dairyTextStyle
                                                        .copyWith(
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                  ),
                                                  SizedBox(
                                                    child: Row(
                                                      children: [
                                                        Text(
                                                          "From Date : ${item['from_date']}-To Date :${item['to_date']}",
                                                          style: dairyTextStyle
                                                              .copyWith(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                              ),
                                                        ),
                                                        const Expanded(
                                                          child: SizedBox(),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                ),
                              ],
                            ),
                          );
                        }
                      },
                    );
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool showPresentOnly = false;
  bool showAbsenteOnly = false;
  @override
  Widget build(BuildContext context) {
    print(
      'Present ${jsondata.where((record) => record['status'] == 2 && DateFormat('dd/MM/yy').parse(record['date']).month == _selectedDate.month && DateFormat('dd/MM/yy').parse(record['date']).year == _selectedDate.year).length}',
    );

    CalendarFormat _calendarFormat = CalendarFormat.month;
    DateTime _focusedDay = DateTime.now();
    DateTime? _selectedDay;
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size(double.infinity, 9.0.hp),
        child: CustomizedAppBar(
          back: back,
          profile: () {},
          screenName: 'Kid’s Attendance',
          screen_id: 2,
        ),
      ),
      backgroundColor: Colors.white,
      body: Padding(
        padding: EdgeInsets.only(top: 30),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: Icon(Icons.arrow_back_ios),
                  onPressed: _previousMonth,
                ),
                Text(
                  DateFormat('MMMM yyyy').format(_selectedDate),
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: Icon(Icons.arrow_forward_ios),
                  onPressed: _nextMonth,
                ),
              ],
            ),
            Padding(
              padding: EdgeInsets.only(left: 15, right: 15, top: 15),
              child: Container(
                width: double.infinity,
                height: 50,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(40),
                  color: Color(0xffeeeeee),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            showPresentOnly = false;
                            showAbsenteOnly = false;
                          });
                        },
                        child: Text(
                          'Days in total ${presentfilteredRecords + absentfilteredRecords} ',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Color(0xff000000),
                            fontSize: 15,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          print('present only');
                          setState(() {
                            showAbsenteOnly = false;
                            showPresentOnly = true;
                          });
                        },
                        child: Text(
                          'Present ${presentfilteredRecords}',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Color(0xff000000),
                            fontSize: 15,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            showPresentOnly = false;
                            showAbsenteOnly = true;
                          });
                        },
                        child: Text(
                          'Absent ${absentfilteredRecords}',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Color(0xff000000),
                            fontSize: 15,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              child:
                  (() {
                    if (showPresentOnly) {
                      print('if container');
                      return present();
                    } else if (showAbsenteOnly) {
                      print('if container');
                      return absent();
                    } else {
                      print('else container');
                      return alldata();
                    }
                  })(),
            ),
          ],
        ),
      ),
    );
  }

  int daysInCurrentMonth() {
    // Get the current date
    DateTime currentDate = DateTime.now();

    // Navigate to the next month
    DateTime nextMonth = DateTime(
      _selectedDate.year,
      _selectedDate.month + 1,
      1,
    );

    // Subtract one day from the next month to get the last day of the current month
    DateTime lastDayOfMonth = nextMonth.subtract(Duration(days: 1));

    // Return the day of the month of the last day of the current month
    return lastDayOfMonth.day;
  }

  Widget alldata() {
    print('all data function call');
    return Container(
      child: ListView.separated(
        separatorBuilder: (BuildContext context, int index) {
          return Padding(
            padding: EdgeInsets.only(top: 10), // Add padding to the top
            child: Divider(height: 1),
          );
        },
        itemCount: daysInMonth(_selectedDate.month, _selectedDate.year),
        itemBuilder: (context, index) {
          DateTime currentDate = DateTime(
            _selectedDate.year,
            _selectedDate.month,
            index + 1,
          );

          var attendanceRecord;
          if (jsondata != null) {
            for (var record in jsondata) {
              if (record['date'] == null) continue;
              try {
                DateTime recordDate = DateFormat(
                  'dd/MM/yy',
                ).parse(record['date']);
                if (currentDate.day == recordDate.day &&
                    currentDate.month == recordDate.month &&
                    currentDate.year == recordDate.year) {
                  attendanceRecord = record;
                  break;
                }
              } catch (e) {
                continue;
              }
            }
          }

          Color? customGreenColor;
          String attendanceStatus;
          if (attendanceRecord != null) {
            if (attendanceRecord['status'] == 2) {
              attendanceStatus = 'Present';
              customGreenColor = Colors.green;
            } else if (attendanceRecord['status'] == 0) {
              attendanceStatus = 'Absent';

              customGreenColor = Colors.red;
            } else {
              attendanceStatus = '----';
              customGreenColor = Colors.green;
            }
          } else {
            attendanceStatus = '----';
            customGreenColor = Colors.green;
          }

          return Padding(
            padding: EdgeInsets.only(left: 30, top: 25, bottom: 5),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${DateFormat('MMM').format(_selectedDate)} ${DateFormat('d').format(currentDate)}',
                  style: dairyTextStyle.copyWith(
                    color: Color(0xff000000),
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${DateFormat('EEEE ').format(currentDate)}',
                        style: dairyTextStyle.copyWith(
                          color: Color(0xff000000),
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(left: 100),
                        child: Text(
                          attendanceStatus.toString(),
                          style: dairyTextStyle.copyWith(
                            color: customGreenColor,
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget absent() {
    return Container(
      child: ListView.separated(
        separatorBuilder: (BuildContext context, int index) {
          return Padding(
            padding: EdgeInsets.only(top: 10), // Add padding to the top
            child: Divider(height: 1),
          );
        },
        itemCount:
            jsondata
                .where(
                  (record) =>
                      record['status'] == 0 &&
                      DateFormat('dd/MM/yy').parse(record['date']).month ==
                          _selectedDate.month &&
                      DateFormat('dd/MM/yy').parse(record['date']).year ==
                          _selectedDate.year,
                )
                .length,
        itemBuilder: (context, index) {
          String dateString =
              jsondata
                  .where(
                    (record) =>
                        record['status'] == 0 &&
                        DateFormat('dd/MM/yy').parse(record['date']).month ==
                            _selectedDate.month &&
                        DateFormat('dd/MM/yy').parse(record['date']).year ==
                            _selectedDate.year,
                  )
                  .toList()[index]['date'];

          DateTime currentDate = DateFormat('dd/MM/yy').parse(dateString);
          print(jsondata);

          return Padding(
            padding: EdgeInsets.only(left: 30, top: 25, bottom: 5),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${DateFormat('MMM').format(_selectedDate)} ${DateFormat('d').format(currentDate)}',
                  style: dairyTextStyle.copyWith(
                    color: Color(0xff000000),
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${DateFormat('EEEE ').format(currentDate)}',
                        style: dairyTextStyle.copyWith(
                          color: Color(0xff000000),
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(left: 100),
                        child: Text(
                          'Absent',
                          style: dairyTextStyle.copyWith(
                            color: Colors.red,
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget present() {
    print('present statement called');
    print(jsonData);
    return Container(
      child: ListView.separated(
        separatorBuilder: (BuildContext context, int index) {
          return Padding(
            padding: EdgeInsets.only(top: 10), // Add padding to the top
            child: Divider(height: 1),
          );
        },
        itemCount:
            jsondata
                .where(
                  (record) =>
                      record['status'] == 2 &&
                      DateFormat('dd/MM/yy').parse(record['date']).month ==
                          _selectedDate.month &&
                      DateFormat('dd/MM/yy').parse(record['date']).year ==
                          _selectedDate.year,
                )
                .toSet()
                .length,
        itemBuilder: (context, index) {
          String dateString =
              jsondata
                  .where(
                    (record) =>
                        record['status'] == 2 &&
                        DateFormat('dd/MM/yy').parse(record['date']).month ==
                            _selectedDate.month &&
                        DateFormat('dd/MM/yy').parse(record['date']).year ==
                            _selectedDate.year,
                  )
                  .toSet() //
                  .toList()[index]['date'];

          DateTime currentDate = DateFormat('dd/MM/yy').parse(dateString);

          return Padding(
            padding: EdgeInsets.only(left: 30, top: 25, bottom: 5),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${DateFormat('MMM').format(_selectedDate)} ${DateFormat('d').format(currentDate)}',
                  style: dairyTextStyle.copyWith(
                    color: Color(0xff000000),
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${DateFormat('EEEE ').format(currentDate)}',
                        style: dairyTextStyle.copyWith(
                          color: Color(0xff000000),
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(left: 100),
                        child: Text(
                          'Present',
                          style: dairyTextStyle.copyWith(
                            color: Colors.green,
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
