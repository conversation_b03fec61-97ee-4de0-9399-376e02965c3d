import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/controller/sample.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';

class EventGallery extends StatefulWidget {
  const EventGallery({super.key});

  @override
  State<EventGallery> createState() => _EventGalleryState();
}

class _EventGalleryState extends State<EventGallery> {
  // SampleController sampleController = Get.put(SampleController());
  @override
  void initState() {
    // fetchdata();
    // TODO: implement initState
    super.initState();
  }

  // Future fetchdata() async {
  //   // await sampleController.fetchData();
  // }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: AppColors.primary, // Match the app bar green color
        statusBarIconBrightness:
            Brightness.light, // Light icons for dark status bar
        statusBarBrightness: Brightness.dark, // For iOS
      ),
    );

    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: Container(
            decoration: const BoxDecoration(
              gradient: AppColors.primaryGradient,
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: IconButton(
                onPressed: back,
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: AppColors.textOnPrimary,
                  size: 24,
                ),
              ),
              title: Text(
                'Event Gallery',
                style: AppTextStyles.headlineSmall.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: FontWeight.w700,
                  fontSize: 22,
                ),
              ),
              centerTitle: true,
            ),
          ),
        ),
        body: Container(),
        bottomNavigationBar: Container(
          height: MediaQuery.of(context).padding.bottom,
          color: AppColors.background,
        ),
      ),
    );
  }
}
