// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/main.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:silverleaf/utils.dart';

// Professional design constants for Attendance
class AttendanceColors {
  static const Color primary = Color(0xFF2E7D32);
  static const Color primaryLight = Color(0xFF4CAF50);
  static const Color secondary = Color(0xFF1976D2);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color background = Color(0xFFF8F9FA);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFF9E9E9E);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color divider = Color(0xFFE0E0E0);
  static const Color cardShadow = Color(0x1A000000);
  static const Color success = Color(0xFF4CAF50);
  static const Color error = Color(0xFFE53935);
  static const Color warning = Color(0xFFFF9800);

  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFF2E7D32), Color(0xFF4CAF50)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient successGradient = LinearGradient(
    colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient errorGradient = LinearGradient(
    colors: [Color(0xFFE53935), Color(0xFFEF5350)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}

class AttendanceTextStyles {
  static const TextStyle headlineLarge = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w700,
    color: AttendanceColors.textPrimary,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: AttendanceColors.textPrimary,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: AttendanceColors.textPrimary,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: AttendanceColors.textPrimary,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: AttendanceColors.textSecondary,
  );
}

class AttendanceRedesigned extends StatefulWidget {
  const AttendanceRedesigned({super.key});

  @override
  State<AttendanceRedesigned> createState() => _AttendanceRedesignedState();
}

class _AttendanceRedesignedState extends State<AttendanceRedesigned> {
  List userNames = [];
  List jsondata = [];
  late DateTime _selectedDate;
  var presentfilteredRecords = 0;
  var absentfilteredRecords = 0;
  bool showPresentOnly = false;
  bool showAbsenteOnly = false;

  @override
  void initState() {
    super.initState();
    _selectedDate = DateTime.now();
    getListString();
  }

  back() {
    Navigator.pop(context);
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames.isNotEmpty) {
      userNames = storedUserNames;
      fetchleaveData();
    }
  }

  fetchleaveData() async {
    var url = 'https://silverleafms.in/silvar_leaf/api/leave/view-details';
    final data = {
      'user': userNames[0],
      'year_id': userNames[6],
      'class_id': userNames[5],
      'section_id': userNames[4],
      'branch_id': userNames[7],
    };

    final response = await http.post(Uri.parse(url), body: data);
    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);
      final List<Map<String, dynamic>> data = List<Map<String, dynamic>>.from(
        responseBody['data'],
      );

      setState(() {
        jsondata = data;

        // Calculate present records
        presentfilteredRecords =
            jsondata.where((record) {
              if (record['status'] == 2) {
                DateTime recordDate = DateFormat(
                  'dd/MM/yy',
                ).parse(record['date']);
                return recordDate.month == _selectedDate.month &&
                    recordDate.year == _selectedDate.year;
              }
              return false;
            }).length;

        // Calculate absent records
        absentfilteredRecords =
            jsondata.where((record) {
              if (record['status'] == 0) {
                DateTime recordDate = DateFormat(
                  'dd/MM/yy',
                ).parse(record['date']);
                return recordDate.month == _selectedDate.month &&
                    recordDate.year == _selectedDate.year;
              }
              return false;
            }).length;
      });

      return jsondata;
    } else {
      throw Exception('Failed to load data');
    }
  }

  void _previousMonth() {
    setState(() {
      _selectedDate = DateTime(_selectedDate.year, _selectedDate.month - 1, 1);
      fetchleaveData();
    });
  }

  void _nextMonth() {
    setState(() {
      _selectedDate = DateTime(_selectedDate.year, _selectedDate.month + 1, 1);
      fetchleaveData();
    });
  }

  int daysInMonth(int month, int year) {
    return DateTime(year, month + 1, 0).day;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AttendanceColors.background,
      appBar: PreferredSize(
        preferredSize: Size(double.infinity, 9.0.hp),
        child: Container(
          decoration: const BoxDecoration(
            gradient: AttendanceColors.primaryGradient,
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              onPressed: back,
              icon: const Icon(
                Icons.arrow_back_ios,
                color: AttendanceColors.textOnPrimary,
                size: 24,
              ),
            ),
            title: Text(
              'Kid\'s Attendance',
              style: AttendanceTextStyles.headlineSmall.copyWith(
                color: AttendanceColors.textOnPrimary,
                fontWeight: FontWeight.w700,
                fontSize: 22,
              ),
            ),
            centerTitle: true,
          ),
        ),
      ),
      body: Column(
        children: [
          // Month Navigation Header
          Container(
            margin: EdgeInsets.all(4.0.wp),
            padding: EdgeInsets.symmetric(vertical: 2.0.hp, horizontal: 4.0.wp),
            decoration: BoxDecoration(
              color: AttendanceColors.surface,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AttendanceColors.cardShadow,
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  onPressed: _previousMonth,
                  icon: Container(
                    padding: EdgeInsets.all(2.0.wp),
                    decoration: BoxDecoration(
                      color: AttendanceColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.arrow_back_ios,
                      color: AttendanceColors.primary,
                      size: 5.0.wp,
                    ),
                  ),
                ),
                Text(
                  DateFormat('MMMM yyyy').format(_selectedDate),
                  style: AttendanceTextStyles.headlineSmall.copyWith(
                    color: AttendanceColors.textPrimary,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                IconButton(
                  onPressed: _nextMonth,
                  icon: Container(
                    padding: EdgeInsets.all(2.0.wp),
                    decoration: BoxDecoration(
                      color: AttendanceColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.arrow_forward_ios,
                      color: AttendanceColors.primary,
                      size: 5.0.wp,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Statistics Cards
          Container(
            margin: EdgeInsets.symmetric(horizontal: 4.0.wp),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Days',
                    '${presentfilteredRecords + absentfilteredRecords}',
                    AttendanceColors.primary,
                    Icons.calendar_month,
                    () {
                      setState(() {
                        showPresentOnly = false;
                        showAbsenteOnly = false;
                      });
                    },
                    isSelected: !showPresentOnly && !showAbsenteOnly,
                  ),
                ),
                SizedBox(width: 2.0.wp),
                Expanded(
                  child: _buildStatCard(
                    'Present',
                    '$presentfilteredRecords',
                    AttendanceColors.success,
                    Icons.check_circle,
                    () {
                      setState(() {
                        showAbsenteOnly = false;
                        showPresentOnly = true;
                      });
                    },
                    isSelected: showPresentOnly,
                  ),
                ),
                SizedBox(width: 2.0.wp),
                Expanded(
                  child: _buildStatCard(
                    'Absent',
                    '$absentfilteredRecords',
                    AttendanceColors.error,
                    Icons.cancel,
                    () {
                      setState(() {
                        showPresentOnly = false;
                        showAbsenteOnly = true;
                      });
                    },
                    isSelected: showAbsenteOnly,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 2.0.hp),

          // Attendance List
          Expanded(child: _buildAttendanceList()),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String count,
    Color color,
    IconData icon,
    VoidCallback onTap, {
    bool isSelected = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(3.0.wp),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? color.withValues(alpha: 0.1)
                  : AttendanceColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : AttendanceColors.divider,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AttendanceColors.cardShadow,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : AttendanceColors.textSecondary,
              size: 6.0.wp,
            ),
            SizedBox(height: 1.0.hp),
            Text(
              count,
              style: AttendanceTextStyles.headlineSmall.copyWith(
                color: isSelected ? color : AttendanceColors.textPrimary,
                fontWeight: FontWeight.w700,
              ),
            ),
            Text(
              title,
              style: AttendanceTextStyles.bodySmall.copyWith(
                color: isSelected ? color : AttendanceColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceList() {
    if (showPresentOnly) {
      return _buildPresentList();
    } else if (showAbsenteOnly) {
      return _buildAbsentList();
    } else {
      return _buildAllDataList();
    }
  }

  Widget _buildAllDataList() {
    return ListView.separated(
      padding: EdgeInsets.all(4.0.wp),
      separatorBuilder: (context, index) => SizedBox(height: 1.0.hp),
      itemCount: daysInMonth(_selectedDate.month, _selectedDate.year),
      itemBuilder: (context, index) {
        DateTime currentDate = DateTime(
          _selectedDate.year,
          _selectedDate.month,
          index + 1,
        );

        Map<String, dynamic>? attendanceRecord;
        for (var record in jsondata) {
          if (record['date'] == null) continue;
          try {
            DateTime recordDate = DateFormat('dd/MM/yy').parse(record['date']);
            if (currentDate.day == recordDate.day &&
                currentDate.month == recordDate.month &&
                currentDate.year == recordDate.year) {
              attendanceRecord = record;
              break;
            }
          } catch (e) {
            continue;
          }
        }

        String attendanceStatus;
        Color statusColor;
        IconData statusIcon;

        if (attendanceRecord != null) {
          if (attendanceRecord['status'] == 2) {
            attendanceStatus = 'Present';
            statusColor = AttendanceColors.success;
            statusIcon = Icons.check_circle;
          } else if (attendanceRecord['status'] == 0) {
            attendanceStatus = 'Absent';
            statusColor = AttendanceColors.error;
            statusIcon = Icons.cancel;
          } else {
            attendanceStatus = 'No Record';
            statusColor = AttendanceColors.textHint;
            statusIcon = Icons.help_outline;
          }
        } else {
          attendanceStatus = 'No Record';
          statusColor = AttendanceColors.textHint;
          statusIcon = Icons.help_outline;
        }

        return Container(
          padding: EdgeInsets.all(4.0.wp),
          decoration: BoxDecoration(
            color: AttendanceColors.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AttendanceColors.cardShadow,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.0.wp),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(statusIcon, color: statusColor, size: 6.0.wp),
              ),
              SizedBox(width: 4.0.wp),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      DateFormat('MMM d').format(currentDate),
                      style: AttendanceTextStyles.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      DateFormat('EEEE').format(currentDate),
                      style: AttendanceTextStyles.bodyMedium.copyWith(
                        color: AttendanceColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 3.0.wp,
                  vertical: 1.0.hp,
                ),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  attendanceStatus,
                  style: AttendanceTextStyles.bodySmall.copyWith(
                    color: statusColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPresentList() {
    final presentRecords =
        jsondata.where((record) {
          if (record['status'] == 2) {
            DateTime recordDate = DateFormat('dd/MM/yy').parse(record['date']);
            return recordDate.month == _selectedDate.month &&
                recordDate.year == _selectedDate.year;
          }
          return false;
        }).toList();

    if (presentRecords.isEmpty) {
      return _buildEmptyState(
        'No present days found',
        Icons.check_circle,
        AttendanceColors.success,
      );
    }

    return ListView.separated(
      padding: EdgeInsets.all(4.0.wp),
      separatorBuilder: (context, index) => SizedBox(height: 1.0.hp),
      itemCount: presentRecords.length,
      itemBuilder: (context, index) {
        final record = presentRecords[index];
        DateTime currentDate = DateFormat('dd/MM/yy').parse(record['date']);

        return Container(
          padding: EdgeInsets.all(4.0.wp),
          decoration: BoxDecoration(
            color: AttendanceColors.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AttendanceColors.success.withValues(alpha: 0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AttendanceColors.cardShadow,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.0.wp),
                decoration: BoxDecoration(
                  gradient: AttendanceColors.successGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.check_circle,
                  color: AttendanceColors.textOnPrimary,
                  size: 6.0.wp,
                ),
              ),
              SizedBox(width: 4.0.wp),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      DateFormat('MMM d').format(currentDate),
                      style: AttendanceTextStyles.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      DateFormat('EEEE').format(currentDate),
                      style: AttendanceTextStyles.bodyMedium.copyWith(
                        color: AttendanceColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 3.0.wp,
                  vertical: 1.0.hp,
                ),
                decoration: BoxDecoration(
                  gradient: AttendanceColors.successGradient,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Present',
                  style: AttendanceTextStyles.bodySmall.copyWith(
                    color: AttendanceColors.textOnPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAbsentList() {
    final absentRecords =
        jsondata.where((record) {
          if (record['status'] == 0) {
            DateTime recordDate = DateFormat('dd/MM/yy').parse(record['date']);
            return recordDate.month == _selectedDate.month &&
                recordDate.year == _selectedDate.year;
          }
          return false;
        }).toList();

    if (absentRecords.isEmpty) {
      return _buildEmptyState(
        'No absent days found',
        Icons.cancel,
        AttendanceColors.error,
      );
    }

    return ListView.separated(
      padding: EdgeInsets.all(4.0.wp),
      separatorBuilder: (context, index) => SizedBox(height: 1.0.hp),
      itemCount: absentRecords.length,
      itemBuilder: (context, index) {
        final record = absentRecords[index];
        DateTime currentDate = DateFormat('dd/MM/yy').parse(record['date']);

        return Container(
          padding: EdgeInsets.all(4.0.wp),
          decoration: BoxDecoration(
            color: AttendanceColors.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AttendanceColors.error.withValues(alpha: 0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AttendanceColors.cardShadow,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.0.wp),
                decoration: BoxDecoration(
                  gradient: AttendanceColors.errorGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.cancel,
                  color: AttendanceColors.textOnPrimary,
                  size: 6.0.wp,
                ),
              ),
              SizedBox(width: 4.0.wp),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      DateFormat('MMM d').format(currentDate),
                      style: AttendanceTextStyles.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      DateFormat('EEEE').format(currentDate),
                      style: AttendanceTextStyles.bodyMedium.copyWith(
                        color: AttendanceColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 3.0.wp,
                  vertical: 1.0.hp,
                ),
                decoration: BoxDecoration(
                  gradient: AttendanceColors.errorGradient,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Absent',
                  style: AttendanceTextStyles.bodySmall.copyWith(
                    color: AttendanceColors.textOnPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(String message, IconData icon, Color color) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color.withValues(alpha: 0.5), size: 15.0.wp),
          SizedBox(height: 2.0.hp),
          Text(
            message,
            style: AttendanceTextStyles.headlineSmall.copyWith(
              color: AttendanceColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 1.0.hp),
          Text(
            'Try selecting a different filter or month',
            style: AttendanceTextStyles.bodyMedium.copyWith(
              color: AttendanceColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
