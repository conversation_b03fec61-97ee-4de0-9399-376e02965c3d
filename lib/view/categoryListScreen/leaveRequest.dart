// ignore_for_file: avoid_print, prefer_interpolation_to_compose_strings, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/main.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';
import 'package:silverleaf/view/teachers_Screeen/leaverequestListTeacherScreen.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:silverleaf/controller/student_leave_controller.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LeaveRequest extends StatefulWidget {
  const LeaveRequest({super.key});

  @override
  State<LeaveRequest> createState() => _LeaveRequestState();
}

class _LeaveRequestState extends State<LeaveRequest> {
  StudentLeaveController studentController = StudentLeaveController();
  TextEditingController leavestatus = TextEditingController();
  TextEditingController descriptionController = TextEditingController();
  TextEditingController fromdate = TextEditingController();
  TextEditingController todate = TextEditingController();

  List<String> list = <String>[
    'Select',
    'Sick',
    'Personal',
    'Family Function',
    'other',
  ];
  bool isLoading = false;
  String dropdownValue = '';
  String? selectedValue;
  final String defaultValue = '3';
  int count = 0;
  List userNames = [];
  final List<DropdownMenuItem<String>> items = [
    DropdownMenuItem(value: '1', child: Center(child: Text('Select'))),
    DropdownMenuItem(value: '2', child: Center(child: Text('Sick'))),
    DropdownMenuItem(value: '3', child: Center(child: Text('Personal'))),
    DropdownMenuItem(value: '4', child: Center(child: Text('Family Function'))),
    DropdownMenuItem(value: '5', child: Center(child: Text('Other'))),
  ];

  String _selectedDate = '';
  DateTime? selectedDateandYear;
  String _selectedEndDate = '';
  String showStartingDate = '';
  String showEndDate = '';
  final DateRangePickerController fromDateController =
      DateRangePickerController();
  final DateRangePickerController toDateController =
      DateRangePickerController();

  @override
  void initState() {
    super.initState();
    selectedValue = '1';
    getListString();
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    setState(() {
      userNames = storedUserNames;
    });
  }

  void _onSelectionChanged(DateRangePickerSelectionChangedArgs args) {
    setState(() {
      if (args.value is DateTime) {
        _selectedDate = DateFormat('dd/MM/yyyy').format(args.value);
        print("Select" + _selectedDate);
      }
    });
  }

  void _onSelectionEndDateChanged(DateRangePickerSelectionChangedArgs args) {
    setState(() {
      if (args.value is DateTime) {
        _selectedEndDate = DateFormat('dd/MM/yyyy').format(args.value);
        print("Select" + _selectedEndDate);
      }
    });
  }

  Widget modernDateContainer({required String title, required IconData icon}) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 4.0.wp, vertical: 2.0.hp),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.divider, width: 1),
      ),
      child: Row(
        children: [
          Icon(icon, color: AppColors.primary, size: 20),
          SizedBox(width: 3.0.wp),
          Expanded(
            child: Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                color:
                    title == 'Select Date'
                        ? AppColors.textHint
                        : AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _submitLeaveRequest() async {
    if (mounted) {
      try {
        leavestatus.text = selectedValue!;
        fromdate.text = _selectedEndDate;
        todate.text = _selectedDate;

        final Map<String, dynamic> jsonData = await studentController
            .savestudent(
              leavestatus.text,
              descriptionController.text,
              fromdate.text,
              todate.text,
              userNames[0],
              userNames[14],
              userNames[6],
              userNames[7],
              userNames[5],
            );

        if (mounted) {
          setState(() {
            isLoading = false;
          });

          if (jsonData['status'].toString() == 'saved') {
            _showSuccessDialog();
          } else {
            _showErrorDialog();
          }
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            isLoading = false;
          });
          _showErrorDialog();
        }
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: EdgeInsets.all(6.0.wp),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.all(4.0.wp),
                  decoration: BoxDecoration(
                    color: AppColors.success.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check_circle,
                    color: AppColors.success,
                    size: 48,
                  ),
                ),
                SizedBox(height: 3.0.hp),
                Text(
                  'Success!',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.0.hp),
                Text(
                  'Your leave request has been submitted successfully',
                  textAlign: TextAlign.center,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(height: 3.0.hp),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      Get.back();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.textOnPrimary,
                      padding: EdgeInsets.symmetric(vertical: 2.0.hp),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'OK',
                      style: AppTextStyles.labelLarge.copyWith(
                        color: AppColors.textOnPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showErrorDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: EdgeInsets.all(6.0.wp),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.all(4.0.wp),
                  decoration: BoxDecoration(
                    color: AppColors.error.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.error, color: AppColors.error, size: 48),
                ),
                SizedBox(height: 3.0.hp),
                Text(
                  'Error',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.0.hp),
                Text(
                  'Failed to submit leave request. Please try again.',
                  textAlign: TextAlign.center,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(height: 3.0.hp),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.error,
                      foregroundColor: AppColors.textOnPrimary,
                      padding: EdgeInsets.symmetric(vertical: 2.0.hp),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'Try Again',
                      style: AppTextStyles.labelLarge.copyWith(
                        color: AppColors.textOnPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  showMyDialog(context) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.all(4.0.wp),
                  decoration: BoxDecoration(
                    gradient: AppColors.primaryGradient,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        color: AppColors.textOnPrimary,
                        size: 24,
                      ),
                      SizedBox(width: 3.0.wp),
                      Text(
                        'Select From Date',
                        style: AppTextStyles.headlineSmall.copyWith(
                          color: AppColors.textOnPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  height: 45.0.hp,
                  padding: EdgeInsets.all(4.0.wp),
                  child: SfDateRangePicker(
                    view: DateRangePickerView.month,
                    selectionMode: DateRangePickerSelectionMode.single,
                    initialSelectedDate: selectedDateandYear,
                    showNavigationArrow: true,
                    controller: fromDateController,
                    onSelectionChanged: _onSelectionChanged,
                    selectionColor: AppColors.primary,
                    todayHighlightColor: AppColors.primaryLight,
                    headerStyle: DateRangePickerHeaderStyle(
                      textStyle: AppTextStyles.headlineSmall.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    monthViewSettings: DateRangePickerMonthViewSettings(
                      viewHeaderStyle: DateRangePickerViewHeaderStyle(
                        textStyle: AppTextStyles.labelMedium.copyWith(
                          color: AppColors.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.all(4.0.wp),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: Text(
                          'Cancel',
                          style: AppTextStyles.labelLarge.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ),
                      SizedBox(width: 2.0.wp),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            showStartingDate = _selectedDate;
                            Navigator.of(context).pop();
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: AppColors.textOnPrimary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Select',
                          style: AppTextStyles.labelLarge.copyWith(
                            color: AppColors.textOnPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  showEndDateMyDialog(context) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.all(4.0.wp),
                  decoration: BoxDecoration(
                    gradient: AppColors.primaryGradient,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        color: AppColors.textOnPrimary,
                        size: 24,
                      ),
                      SizedBox(width: 3.0.wp),
                      Text(
                        'Select To Date',
                        style: AppTextStyles.headlineSmall.copyWith(
                          color: AppColors.textOnPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  height: 45.0.hp,
                  padding: EdgeInsets.all(4.0.wp),
                  child: SfDateRangePicker(
                    view: DateRangePickerView.month,
                    showNavigationArrow: true,
                    selectionMode: DateRangePickerSelectionMode.single,
                    initialDisplayDate: selectedDateandYear,
                    controller: toDateController,
                    onSelectionChanged: _onSelectionEndDateChanged,
                    selectionColor: AppColors.primary,
                    todayHighlightColor: AppColors.primaryLight,
                    headerStyle: DateRangePickerHeaderStyle(
                      textStyle: AppTextStyles.headlineSmall.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    monthViewSettings: DateRangePickerMonthViewSettings(
                      viewHeaderStyle: DateRangePickerViewHeaderStyle(
                        textStyle: AppTextStyles.labelMedium.copyWith(
                          color: AppColors.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.all(4.0.wp),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: Text(
                          'Cancel',
                          style: AppTextStyles.labelLarge.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ),
                      SizedBox(width: 2.0.wp),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            showEndDate = _selectedEndDate;
                            Navigator.of(context).pop();
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: AppColors.textOnPrimary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Select',
                          style: AppTextStyles.labelLarge.copyWith(
                            color: AppColors.textOnPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return isTeacher == true
        ? LeaveRequestListTeacherScreen()
        : Scaffold(
          backgroundColor: AppColors.background,
          appBar: PreferredSize(
            preferredSize: Size(double.infinity, 9.0.hp),
            child: Container(
              decoration: const BoxDecoration(
                gradient: AppColors.primaryGradient,
              ),
              child: SafeArea(
                bottom: false,
                child: AppBar(
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  leading: IconButton(
                    onPressed: back,
                    icon: const Icon(
                      Icons.arrow_back_ios,
                      color: AppColors.textOnPrimary,
                      size: 24,
                    ),
                  ),
                  title: Text(
                    'Leave Request',
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: AppColors.textOnPrimary,
                      fontWeight: FontWeight.w700,
                      fontSize: 22,
                    ),
                  ),
                  centerTitle: true,
                ),
              ),
            ),
          ),
          body: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Padding(
              padding: EdgeInsets.all(6.0.wp),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 2.0.hp),

                  // Header Card
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(4.0.wp),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.cardShadow,
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(2.0.wp),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.event_note,
                                color: AppColors.primary,
                                size: 24,
                              ),
                            ),
                            SizedBox(width: 3.0.wp),
                            Text(
                              'Submit Leave Request',
                              style: AppTextStyles.headlineSmall.copyWith(
                                color: AppColors.textPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 1.0.hp),
                        Text(
                          'Please fill in the details below to submit your leave request',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 3.0.hp),

                  // Leave Type Selection Card
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(4.0.wp),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.cardShadow,
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Leave Type',
                          style: AppTextStyles.labelLarge.copyWith(
                            color: AppColors.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: 2.0.hp),
                        DropdownButtonFormField<String>(
                          isExpanded: true,
                          value: selectedValue,
                          items: items.toList(),
                          decoration: InputDecoration(
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 4.0.wp,
                              vertical: 2.0.hp,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.divider,
                                width: 1,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.primary,
                                width: 2,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.divider,
                                width: 1,
                              ),
                            ),
                            filled: true,
                            fillColor: AppColors.surfaceVariant,
                          ),
                          onChanged: (String? value) {
                            setState(() {
                              if (value != null && value != '') {
                                selectedValue = value;
                              }
                            });
                          },
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 3.0.hp),

                  // Description Card
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(4.0.wp),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.cardShadow,
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Description',
                          style: AppTextStyles.labelLarge.copyWith(
                            color: AppColors.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: 2.0.hp),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.divider,
                              width: 1,
                            ),
                          ),
                          child: TextField(
                            controller: descriptionController,
                            maxLines: 6,
                            keyboardType: TextInputType.multiline,
                            style: AppTextStyles.bodyMedium,
                            decoration: InputDecoration(
                              hintText:
                                  'Please provide details about your leave request...',
                              hintStyle: AppTextStyles.bodyMedium.copyWith(
                                color: AppColors.textHint,
                              ),
                              contentPadding: EdgeInsets.all(4.0.wp),
                              border: InputBorder.none,
                              filled: true,
                              fillColor: AppColors.surfaceVariant,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 3.0.hp),

                  // Date Selection Card
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(4.0.wp),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.cardShadow,
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Leave Duration',
                          style: AppTextStyles.labelLarge.copyWith(
                            color: AppColors.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: 2.0.hp),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'From Date',
                                    style: AppTextStyles.bodySmall.copyWith(
                                      color: AppColors.textSecondary,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  SizedBox(height: 1.0.hp),
                                  GestureDetector(
                                    onTap: () => showMyDialog(context),
                                    child: modernDateContainer(
                                      title:
                                          showStartingDate == ''
                                              ? 'Select Date'
                                              : showStartingDate,
                                      icon: Icons.calendar_today,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 4.0.wp),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'To Date',
                                    style: AppTextStyles.bodySmall.copyWith(
                                      color: AppColors.textSecondary,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  SizedBox(height: 1.0.hp),
                                  GestureDetector(
                                    onTap: () => showEndDateMyDialog(context),
                                    child: modernDateContainer(
                                      title:
                                          showEndDate == ''
                                              ? 'Select Date'
                                              : showEndDate,
                                      icon: Icons.calendar_today,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 4.0.hp),

                  // Submit Button
                  GestureDetector(
                    onTap: () {
                      if (!isLoading) {
                        setState(() {
                          isLoading = true;
                        });
                        _submitLeaveRequest();
                      }
                    },
                    child: Container(
                      width: double.infinity,
                      height: 6.0.hp,
                      decoration: BoxDecoration(
                        gradient: AppColors.primaryGradient,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withValues(alpha: 0.3),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Center(
                        child:
                            isLoading
                                ? const CircularProgressIndicator(
                                  color: AppColors.textOnPrimary,
                                )
                                : Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.send,
                                      color: AppColors.textOnPrimary,
                                      size: 20,
                                    ),
                                    SizedBox(width: 2.0.wp),
                                    Text(
                                      'Submit Request',
                                      style: AppTextStyles.labelLarge.copyWith(
                                        color: AppColors.textOnPrimary,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                      ),
                    ),
                  ),

                  SizedBox(height: 3.0.hp),
                ],
              ),
            ),
          ),
          bottomNavigationBar: Container(
            height: MediaQuery.of(context).padding.bottom,
            color: AppColors.background,
          ),
        );
  }
}
