// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

class CalenderOfEvents extends StatefulWidget {
  const CalenderOfEvents({super.key});

  @override
  State<CalenderOfEvents> createState() => _CalenderOfEventsState();
}

class _CalenderOfEventsState extends State<CalenderOfEvents> {
  back() {
    Get.back();
  }

  dynamic jsonData;
  int type = 0;
  List userNames = [];

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    setState(() {
      userNames = storedUserNames;
    });
  }

  @override
  void initState() {
    getListString();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: PreferredSize(
        preferredSize: Size(double.infinity, 9.0.hp),
        child: Container(
          decoration: const BoxDecoration(gradient: AppColors.primaryGradient),
          child: SafeArea(
            bottom: false,
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: IconButton(
                onPressed: back,
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: AppColors.textOnPrimary,
                  size: 24,
                ),
              ),
              title: Text(
                'Calendar of Events',
                style: AppTextStyles.headlineSmall.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: FontWeight.w700,
                  fontSize: 22,
                ),
              ),
              centerTitle: true,
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          SizedBox(height: 2.0.hp),

          // Filter Section
          Container(
            margin: EdgeInsets.symmetric(horizontal: 6.0.wp),
            padding: EdgeInsets.all(4.0.wp),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppColors.cardShadow,
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(2.0.wp),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.filter_alt_outlined,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
                SizedBox(width: 3.0.wp),
                Text(
                  'Filter Events',
                  style: AppTextStyles.labelLarge.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Row(
                  children: [
                    modernFilterButton('Holidays', typeEvent == 0, () {
                      setState(() {
                        typeEvent = 0;
                        type = 1;
                      });
                    }),
                    SizedBox(width: 3.0.wp),
                    modernFilterButton('Others', typeEvent == 1, () {
                      setState(() {
                        typeEvent = 1;
                        type = 2;
                      });
                    }),
                  ],
                ),
              ],
            ),
          ),

          SizedBox(height: 2.0.hp),

          Expanded(child: eventsWidget()),
        ],
      ),
      bottomNavigationBar: Container(
        height: MediaQuery.of(context).padding.bottom,
        color: AppColors.background,
      ),
    );
  }

  Future<List> fetchcalendarData() async {
    print(type);
    final url =
        'https://silverleafms.in/silvar_leaf/api/calendar/view-calendar/$type/${userNames[7]}/${userNames[14]}';

    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      final responseBody = json.decode(response.body);
      // List<dynamic> result = responseBody['data'];
      this.jsonData = responseBody['data'];
      print(this.jsonData);
      return this.jsonData;
    } else {
      throw Exception('Failed to load data');
    }
  }

  String getMonthName(int monthNumber) {
    print(monthNumber);
    if (monthNumber < 1 || monthNumber > 12) {
      throw ArgumentError('Month number should be between 1 and 12');
    }
    List<String> monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    print(monthNames[monthNumber - 1]);
    return monthNames[monthNumber - 1];
    // return monthNumber.toString();
  }

  Widget modernFilterButton(String title, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 4.0.wp, vertical: 1.5.hp),
        decoration: BoxDecoration(
          gradient: isSelected ? AppColors.primaryGradient : null,
          color: isSelected ? null : AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? Colors.transparent : AppColors.divider,
            width: 1,
          ),
        ),
        child: Text(
          title,
          style: AppTextStyles.labelMedium.copyWith(
            color:
                isSelected ? AppColors.textOnPrimary : AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget eventsWidget() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 6.0.wp),
      child: FutureBuilder<List<dynamic>>(
        future: fetchcalendarData(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: AppColors.primary),
                  SizedBox(height: 2.0.hp),
                  Text(
                    'Loading events...',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            );
          } else if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, color: AppColors.error, size: 48),
                  SizedBox(height: 2.0.hp),
                  Text(
                    'Failed to load events',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 1.0.hp),
                  Text(
                    'Please check your connection and try again',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            );
          } else {
            if (snapshot.data!.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.event_busy, color: AppColors.textHint, size: 64),
                    SizedBox(height: 2.0.hp),
                    Text(
                      'No Events Found',
                      style: AppTextStyles.headlineSmall.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 1.0.hp),
                    Text(
                      'There are no events scheduled at the moment',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              );
            } else {
              List<dynamic> data = snapshot.data!;

              return ListView.separated(
                separatorBuilder: (context, index) {
                  return SizedBox(height: 2.0.hp);
                },
                padding: EdgeInsets.symmetric(vertical: 2.0.hp),
                itemCount: snapshot.data!.length,
                itemBuilder: (context, index) {
                  final item = data[index];

                  var month = getMonthName(
                    int.parse(item['event_date'].toString().substring(3, 5)),
                  );

                  return Container(
                    padding: EdgeInsets.all(4.0.wp),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.cardShadow,
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 20.0.wp,
                          height: 20.0.wp,
                          decoration: BoxDecoration(
                            gradient: AppColors.primaryGradient,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primary.withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                item['event_date'].toString().substring(0, 2),
                                style: AppTextStyles.headlineSmall.copyWith(
                                  color: AppColors.textOnPrimary,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              Text(
                                month.substring(0, 3).toUpperCase(),
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.textOnPrimary,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 10,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 4.0.wp),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                item['event_title'],
                                style: AppTextStyles.headlineSmall.copyWith(
                                  color: AppColors.textPrimary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(height: 1.0.hp),
                              Text(
                                item['description'].toString(),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: AppColors.textSecondary,
                                  height: 1.4,
                                ),
                              ),
                              SizedBox(height: 1.0.hp),
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 3.0.wp,
                                  vertical: 0.5.hp,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withValues(
                                    alpha: 0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  typeEvent == 0 ? 'Holiday' : 'Event',
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            }
          }
        },
      ),
    );
  }

  var typeEvent = 3;
}
