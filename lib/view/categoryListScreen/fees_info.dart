// ignore_for_file: avoid_unnecessary_containers, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

class FeesInfo extends StatefulWidget {
  const FeesInfo({super.key});

  @override
  State<FeesInfo> createState() => _FeesInfoState();
}

class _FeesInfoState extends State<FeesInfo> {
  back() {
    Get.back();
  }

  var jsonData;
  List userNames = [];
  List<dynamic> student_detail = [];
  var sum;
  Future<List> fetchFeesData() async {
    final url =
        'https://silverleafms.in/silvar_leaf/api/fees/view-student-fees-new/${userNames[0]}/${userNames[6]}/${userNames[14]}';

    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      final responseBody = json.decode(response.body);
      // List<dynamic> result = responseBody['data'];

      this.jsonData = responseBody['data'];

      setState(() {
        final List<dynamic> data = this.jsonData;
        this.student_detail =
            data.map((item) {
              return {
                'total': item['term1'] + item['term2'] + item['term3'],
                'amount': item['amount'] == null ? 0 : item['amount'],
                'fees_amount':
                    item['fees_amount'] == null ? 0 : item['fees_amount'],
                'discount':
                    item['total_discount'] == null
                        ? 0
                        : int.parse(item['total_discount']),
                'paid': item['paid'],
              };
            }).toList();
      });
      print('api response all');
      print(this.jsonData[0]['paid'].length);
      // int sum = this
      //     .student_detail
      //     .fold(0, (prev, element) => prev + element['amount']!);

      return this.jsonData;
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;
        fetchFeesData();
      });
    }
  }

  String formatAmountInRupees(amount) {
    // Format the amount as Indian Rupees
    final rupeesFormat = NumberFormat.currency(locale: 'en_IN', symbol: '₹');
    return rupeesFormat.format(amount);
  }

  void initState() {
    //  this.fetchcalendarData();
    super.initState();
    getListString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: PreferredSize(
        preferredSize: Size(double.infinity, 9.0.hp),
        child: Container(
          decoration: const BoxDecoration(gradient: AppColors.primaryGradient),
          child: SafeArea(
            bottom: false,
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: IconButton(
                onPressed: back,
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: AppColors.textOnPrimary,
                  size: 24,
                ),
              ),
              title: Text(
                'Fees Info',
                style: AppTextStyles.headlineSmall.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: FontWeight.w700,
                  fontSize: 22,
                ),
              ),
              centerTitle: true,
            ),
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFFAFAFA), Color(0xFFFFFFFF)],
          ),
        ),
        child:
            this.jsonData == null
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(color: AppColors.primary),
                      SizedBox(height: 2.0.hp),
                      Text(
                        'Loading fees information...',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                )
                : SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Padding(
                    padding: EdgeInsets.all(6.0.wp),
                    child: Column(
                      children: [
                        // Fees Breakdown Card
                        Container(
                          margin: EdgeInsets.only(bottom: 3.0.hp),
                          decoration: BoxDecoration(
                            color: AppColors.surface,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.cardShadow,
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Padding(
                            padding: EdgeInsets.all(5.0.wp),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(2.0.wp),
                                      decoration: BoxDecoration(
                                        color: AppColors.primaryLight
                                            .withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Icon(
                                        Icons.account_balance_wallet,
                                        color: AppColors.primary,
                                        size: 20,
                                      ),
                                    ),
                                    SizedBox(width: 3.0.wp),
                                    Text(
                                      "Fees Breakdown",
                                      style: AppTextStyles.headlineSmall
                                          .copyWith(
                                            fontWeight: FontWeight.w700,
                                            color: AppColors.textPrimary,
                                          ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 4.0.wp),
                                // Modern Term Cards - Vertical Layout
                                _buildTermCard(
                                  "Term 1",
                                  "Rs.${formatAmountInRupees(jsonData[0]['term1'])}",
                                  AppColors.primary,
                                ),
                                SizedBox(height: 3.0.wp),
                                _buildTermCard(
                                  "Term 2",
                                  "Rs.${formatAmountInRupees(jsonData[0]['term2'])}",
                                  AppColors.secondary,
                                ),
                                SizedBox(height: 3.0.wp),
                                _buildTermCard(
                                  "Term 3",
                                  "Rs.${formatAmountInRupees(jsonData[0]['term3'])}",
                                  AppColors.accent,
                                ),
                              ],
                            ),
                          ),
                        ),
                        // Settlement Overview Card
                        Container(
                          margin: EdgeInsets.only(bottom: 3.0.hp),
                          decoration: BoxDecoration(
                            color: AppColors.surface,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.cardShadow,
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Padding(
                            padding: EdgeInsets.all(5.0.wp),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(2.0.wp),
                                      decoration: BoxDecoration(
                                        color: AppColors.secondaryLight
                                            .withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Icon(
                                        Icons.receipt_long,
                                        color: AppColors.secondary,
                                        size: 20,
                                      ),
                                    ),
                                    SizedBox(width: 3.0.wp),
                                    Text(
                                      "Settlement Overview",
                                      style: AppTextStyles.headlineSmall
                                          .copyWith(
                                            fontWeight: FontWeight.w700,
                                            color: AppColors.textPrimary,
                                          ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 4.0.wp),
                                _buildInfoRow(
                                  "Total Fees",
                                  "Rs.${formatAmountInRupees(this.student_detail[0]['total'])}",
                                  isHighlighted: true,
                                ),
                                // Payment History
                                if (this
                                    .student_detail[0]['paid']
                                    .isNotEmpty) ...[
                                  SizedBox(height: 2.0.wp),
                                  Text(
                                    "Payment History",
                                    style: AppTextStyles.bodyLarge.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.textPrimary,
                                    ),
                                  ),
                                  SizedBox(height: 2.0.wp),
                                  ...List.generate(
                                    this.student_detail[0]['paid'].length,
                                    (index) {
                                      var payment =
                                          this.student_detail[0]['paid'][index];
                                      return _buildInfoRow(
                                        "Payment ${index + 1} (${payment['amount_date']})",
                                        "Rs.${formatAmountInRupees(payment['amount'])}",
                                      );
                                    },
                                  ),
                                ],
                                _buildInfoRow(
                                  "Discount",
                                  this.student_detail[0]['discount'] != 0
                                      ? "Rs.${formatAmountInRupees(this.student_detail[0]['discount'])}"
                                      : "---",
                                ),
                                _buildInfoRow(
                                  "Additional Fees",
                                  this.student_detail[0]['fees_amount'] != 0
                                      ? "Rs.${formatAmountInRupees(int.parse(this.student_detail[0]['fees_amount']))}"
                                      : "---",
                                ),
                                _buildInfoRow(
                                  "Total Paid",
                                  this.student_detail[0]['amount'] != 0
                                      ? "Rs.${formatAmountInRupees(int.parse(this.student_detail[0]['amount']))}"
                                      : "---",
                                ),
                                _buildInfoRow(
                                  "Balance Fees",
                                  "Rs.${formatAmountInRupees(this.student_detail[0]['total'] - this.student_detail[0]['discount'] - int.parse(this.student_detail[0]['amount'].toString()) + int.parse(this.student_detail[0]['fees_amount'].toString()))}",
                                  isHighlighted: true,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
      ),
      bottomNavigationBar: Container(
        height: MediaQuery.of(context).padding.bottom,
        color: AppColors.background,
      ),
    );
  }

  Widget tables({title, colors, width}) {
    return Container(
      height: 6.0.hp,
      width: width,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: colors,
        border: Border.all(width: 0.5, color: Colors.grey),
      ),
      child: Text(title),
    );
  }

  Widget _buildTermCard(String title, String amount, Color color) {
    return Container(
      padding: EdgeInsets.all(4.0.wp),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(2.0.wp),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.school, color: color, size: 20),
          ),
          SizedBox(width: 4.0.wp),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 1.0.wp),
                Text(
                  amount,
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value, {
    bool isHighlighted = false,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 2.0.wp),
      padding: EdgeInsets.all(4.0.wp),
      decoration: BoxDecoration(
        color:
            isHighlighted
                ? AppColors.primaryLight.withValues(alpha: 0.1)
                : AppColors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              isHighlighted
                  ? AppColors.primary.withValues(alpha: 0.3)
                  : AppColors.divider,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w700,
            ),
          ),
        ],
      ),
    );
  }

  List feesStatement = ['Total Fees', 'Paid Fees', 'Balance Fees'];
}
