// ignore_for_file: avoid_unnecessary_containers, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

class FeesInfo extends StatefulWidget {
  const FeesInfo({super.key});

  @override
  State<FeesInfo> createState() => _FeesInfoState();
}

class _FeesInfoState extends State<FeesInfo> {
  back() {
    Get.back();
  }

  var jsonData;
  List userNames = [];
  List<dynamic> student_detail = [];
  var sum;
  Future<List> fetchFeesData() async {
    final url =
        'https://silverleafms.in/silvar_leaf/api/fees/view-student-fees-new/${userNames[0]}/${userNames[6]}/${userNames[14]}';

    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      final responseBody = json.decode(response.body);
      // List<dynamic> result = responseBody['data'];

      this.jsonData = responseBody['data'];

      setState(() {
        final List<dynamic> data = this.jsonData;
        this.student_detail =
            data.map((item) {
              return {
                'total': item['term1'] + item['term2'] + item['term3'],
                'amount': item['amount'] == null ? 0 : item['amount'],
                'fees_amount':
                    item['fees_amount'] == null ? 0 : item['fees_amount'],
                'discount':
                    item['total_discount'] == null
                        ? 0
                        : int.parse(item['total_discount']),
                'paid': item['paid'],
              };
            }).toList();
      });
      print('api response all');
      print(this.jsonData[0]['paid'].length);
      // int sum = this
      //     .student_detail
      //     .fold(0, (prev, element) => prev + element['amount']!);

      return this.jsonData;
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;
        fetchFeesData();
      });
    }
  }

  String formatAmountInRupees(amount) {
    // Format the amount as Indian Rupees
    final rupeesFormat = NumberFormat.currency(locale: 'en_IN', symbol: '₹');
    return rupeesFormat.format(amount);
  }

  void initState() {
    //  this.fetchcalendarData();
    super.initState();
    getListString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: PreferredSize(
        preferredSize: Size(double.infinity, 9.0.hp),
        child: Container(
          decoration: const BoxDecoration(gradient: AppColors.primaryGradient),
          child: SafeArea(
            bottom: false,
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: IconButton(
                onPressed: back,
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: AppColors.textOnPrimary,
                  size: 24,
                ),
              ),
              title: Text(
                'Fees Info',
                style: AppTextStyles.headlineSmall.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: FontWeight.w700,
                  fontSize: 22,
                ),
              ),
              centerTitle: true,
            ),
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFFAFAFA), Color(0xFFFFFFFF)],
          ),
        ),
        child:
            this.jsonData == null
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(color: AppColors.primary),
                      SizedBox(height: 2.0.hp),
                      Text(
                        'Loading fees information...',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                )
                : SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Padding(
                    padding: EdgeInsets.all(6.0.wp),
                    child: Column(
                      children: [
                        Container(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Fees Breakdown",
                                style: textStyle.copyWith(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14.0.sp,
                                ),
                              ),
                              SizedBox(height: 1.0.hp),
                              SizedBox(
                                child: Row(
                                  children: [
                                    tables(
                                      width:
                                          MediaQuery.of(context).size.width /
                                          3.5,
                                      colors: Color.fromARGB(
                                        255,
                                        229,
                                        247,
                                        220,
                                      ),
                                      title: "Term - 1",
                                    ),
                                    tables(
                                      width:
                                          MediaQuery.of(context).size.width /
                                          3.5,
                                      colors: Color.fromARGB(
                                        255,
                                        229,
                                        247,
                                        220,
                                      ),
                                      title: "Term - 2",
                                    ),
                                    tables(
                                      width:
                                          MediaQuery.of(context).size.width /
                                          3.5,
                                      colors: Color.fromARGB(
                                        255,
                                        229,
                                        247,
                                        220,
                                      ),
                                      title: "Term - 3",
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                child: Row(
                                  children: [
                                    // jsonData?.map((item) {
                                    //   return tables(
                                    //       width: MediaQuery.of(context).size.width / 3.5,
                                    //       colors: Colors.white,
                                    //       title: "Rs. 1,40,000");
                                    // })
                                    tables(
                                      width:
                                          MediaQuery.of(context).size.width /
                                          3.5,
                                      colors: Colors.white,
                                      title: "Rs.${jsonData[0]['term1']}",
                                    ),
                                    tables(
                                      width:
                                          MediaQuery.of(context).size.width /
                                          3.5,
                                      colors: Colors.white,
                                      title: "Rs.${jsonData[0]['term2']}",
                                    ),
                                    tables(
                                      width:
                                          MediaQuery.of(context).size.width /
                                          3.5,
                                      colors: Colors.white,
                                      title: "Rs. ${jsonData[0]['term3']}",
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 11.0.hp),
                            ],
                          ),
                        ),
                        Container(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Settlement Overview",
                                style: textStyle.copyWith(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14.0.sp,
                                ),
                              ),
                              SizedBox(height: 1.0.hp),
                              SizedBox(
                                child: Row(
                                  children: [
                                    tables(
                                      width:
                                          MediaQuery.of(context).size.width /
                                          2.4,
                                      colors: Colors.white,
                                      title: "Total Fees",
                                    ),
                                    Expanded(
                                      child: tables(
                                        width:
                                            MediaQuery.of(context).size.width /
                                            2.4,
                                        colors: Colors.white,
                                        title:
                                            "Rs.${formatAmountInRupees(this.student_detail[0]['total'])}",
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SingleChildScrollView(
                                child: Column(
                                  children: [
                                    ListView.builder(
                                      shrinkWrap: true,
                                      physics:
                                          NeverScrollableScrollPhysics(), // Disable ListView scrolling
                                      itemCount:
                                          this.student_detail[0]['paid'].length,
                                      itemBuilder: (context, index) {
                                        var payment =
                                            this.student_detail[0]['paid'][index];

                                        return Row(
                                          children: [
                                            tables(
                                              width:
                                                  MediaQuery.of(
                                                    context,
                                                  ).size.width /
                                                  2.4,
                                              colors: Color.fromARGB(
                                                255,
                                                229,
                                                247,
                                                220,
                                              ),
                                              title:
                                                  "Paid ${index + 1}\n(${payment['amount_date']})",
                                            ),
                                            Expanded(
                                              child: tables(
                                                width:
                                                    MediaQuery.of(
                                                      context,
                                                    ).size.width /
                                                    2.4,
                                                colors: Color.fromARGB(
                                                  255,
                                                  229,
                                                  247,
                                                  220,
                                                ),
                                                title:
                                                    "Rs.${formatAmountInRupees(payment['amount'])}",
                                              ),
                                            ),
                                          ],
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                child: Row(
                                  children: [
                                    tables(
                                      width:
                                          MediaQuery.of(context).size.width /
                                          2.4,
                                      colors: Colors.white,
                                      title: "Discount",
                                    ),
                                    Expanded(
                                      child: tables(
                                        width:
                                            MediaQuery.of(context).size.width /
                                            2.4,
                                        colors: Colors.white,
                                        title:
                                            this.student_detail[0]['discount'] !=
                                                    0
                                                ? "Rs.${formatAmountInRupees(this.student_detail[0]['discount'])}"
                                                : "---",
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                child: Row(
                                  children: [
                                    tables(
                                      width:
                                          MediaQuery.of(context).size.width /
                                          2.4,
                                      colors: Color.fromARGB(
                                        255,
                                        229,
                                        247,
                                        220,
                                      ),
                                      title: "Additional Fees",
                                    ),
                                    Expanded(
                                      child: tables(
                                        width:
                                            MediaQuery.of(context).size.width /
                                            2.4,
                                        colors: Color.fromARGB(
                                          255,
                                          229,
                                          247,
                                          220,
                                        ),
                                        title:
                                            this.student_detail[0]['fees_amount'] !=
                                                    0
                                                ? "Rs.${formatAmountInRupees(int.parse(this.student_detail[0]['fees_amount']))}"
                                                : "---",
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                child: Row(
                                  children: [
                                    tables(
                                      width:
                                          MediaQuery.of(context).size.width /
                                          2.4,
                                      colors: Colors.white,
                                      title: "Total",
                                    ),
                                    Expanded(
                                      child: tables(
                                        width:
                                            MediaQuery.of(context).size.width /
                                            2.4,
                                        colors: Colors.white,
                                        title:
                                            this.student_detail[0]['amount'] !=
                                                    0
                                                ? "Rs.${formatAmountInRupees(int.parse(this.student_detail[0]['amount']))}"
                                                : "---",
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                child: Row(
                                  children: [
                                    tables(
                                      width:
                                          MediaQuery.of(context).size.width /
                                          2.4,
                                      colors: Color.fromARGB(
                                        255,
                                        229,
                                        247,
                                        220,
                                      ),
                                      title: "Balance Fees",
                                    ),
                                    Expanded(
                                      child: tables(
                                        width:
                                            MediaQuery.of(context).size.width /
                                            2.4,
                                        colors: Color.fromARGB(
                                          255,
                                          229,
                                          247,
                                          220,
                                        ),
                                        title:
                                            this.student_detail.length > 0 &&
                                                    this.student_detail[0]['amount'] !=
                                                        0
                                                ? "Rs.${formatAmountInRupees(this.student_detail[0]['total'] - this.student_detail[0]['discount'] - int.parse(this.student_detail[0]['amount'].toString()) + int.parse(this.student_detail[0]['fees_amount'].toString()))}"
                                                : "Rs.${formatAmountInRupees(this.student_detail[0]['total'] - this.student_detail[0]['discount'] - int.parse(this.student_detail[0]['amount'].toString()) + int.parse(this.student_detail[0]['fees_amount'].toString()))}",
                                      ),
                                      //"Rs.${formatAmountInRupees(this.student_detail[0]['total'] - this.student_detail[0]['discount'] - this.student_detail[0]['amount'] + (this.student_detail[0]['fees_amount'] != null) ? int.parse(this.student_detail[0]['fees_amount']) : 0)}"),
                                    ),
                                  ],
                                ),
                              ),
                              // SizedBox(
                              //     // child: SfDataGrid(
                              //     //   gridLinesVisibility: GridLinesVisibility.both,
                              //     //   headerGridLinesVisibility: GridLinesVisibility.both,
                              //     //   source: employeeDataSource,
                              //     //   // allowEditing: true,
                              //     //   // allowColumnsDragging: true,
                              //     //   // allowColumnsResizing: true,
                              //     //   // allowFiltering: true,
                              //     //   // allowMultiColumnSorting: true,
                              //     //   // allowPullToRefresh: true,
                              //     //   // allowSorting: true,
                              //     //   // allowSwiping: true,
                              //     //   // allowTriStateSorting: true,
                              //     //   columnWidthMode: ColumnWidthMode.fill,
                              //     //   footerHeight: 10.0.hp,
                              //     //   footer: Column(
                              //     //     mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              //     //     children: [
                              //     //       for (var k = 0; k < 1; k++)
                              //     //         Row(
                              //     //           mainAxisAlignment:
                              //     //               MainAxisAlignment.spaceEvenly,
                              //     //           children: [
                              //     //             for (var i = 0; i < 3; i++)
                              //     //               Text(i == 0
                              //     //                   ? 'Rs.1,40,000'
                              //     //                   : i == 1
                              //     //                       ? "Rs.2,30,000"
                              //     //                       : "Rs.1,20,000"),
                              //     //           ],
                              //     //         ),
                              //     //     ],
                              //     //   ),
                              //     //   rowHeight: 10.0.hp, headerRowHeight: 6.5.hp,
                              //     //   columns: <GridColumn>[
                              //     //     for (var i = 0; i < 3; i++)
                              //     //       GridColumn(
                              //     //           columnName: feesStatement[i].toString(),
                              //     //           label: Container(
                              //     //               color: Color.fromARGB(255, 229, 248, 218),
                              //     //               padding: EdgeInsets.all(16.0),
                              //     //               alignment: Alignment.center,
                              //     //               child: Text(
                              //     //                 feesStatement[i].toString(),
                              //     //               ))),
                              //     //   ],
                              //     // ),
                              //     ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
      ),
      bottomNavigationBar: Container(
        height: MediaQuery.of(context).padding.bottom,
        color: AppColors.background,
      ),
    );
  }

  Widget tables({title, colors, width}) {
    return Container(
      height: 6.0.hp,
      width: width,
      alignment: Alignment.center,
      child: Text(title),
      decoration: BoxDecoration(
        color: colors,
        border: Border.all(width: 0.5, color: Colors.grey),
      ),
    );
  }

  List feesStatement = ['Total Fees', 'Paid Fees', 'Balance Fees'];
}
