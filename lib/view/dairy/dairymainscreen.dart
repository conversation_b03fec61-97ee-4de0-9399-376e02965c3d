// ignore_for_file: prefer_const_constructors, avoid_unnecessary_containers

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:silverleaf/contest/color.dart';
// import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/dashboard/dashboardscreen.dart';
// import 'package:silverleaf/view/dairy/DairySubPage.dart';
import 'package:silverleaf/view/dashboard/mainboard.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:silverleaf/widgets/chatWidgets.dart';
import 'package:flutter/services.dart';
import 'dart:io';

import 'package:url_launcher/url_launcher.dart';

var dairypages = 0;

class DairyMainScreen extends StatefulWidget {
  const DairyMainScreen({super.key});

  @override
  State<DairyMainScreen> createState() => _DairyMainScreenState();
}

class _DairyMainScreenState extends State<DairyMainScreen> {
  var jsonData;

  late SharedPreferences prefs;
  final scrollcontroller = ScrollController();
  bool isTeacher = false;
  back() {
    print('back button clicked');
    // Get.to(MainBoard());
    Get.back();
  }

  List dairyCuttentUpdate = ["12 Aug, 2023 | Saturday"];
  List name = [""];
  List content = [];
  List dOS = [];
  List ubName = [];
  List userNames = [];
  List<DropdownMenuItem<String>> branch = [];
  Future<Map<String, List<Map<String, dynamic>>>> fetchdairyData() async {
    Map<String, List<Map<String, dynamic>>> _jsonData = {};
    final url;
    if (isTeacher == false) {
      print(userNames[6]);
      print(userNames[5]);

      print(userNames[14]);
      url =
          'https://silverleafms.in/silvar_leaf/api/dairy/view-student-diary/${userNames[6]}/${userNames[5]}/${userNames[14]}';
    } else {
      url =
          'https://silverleafms.in/silvar_leaf/api/dairy/view-student-diary/${userNames[7]}/${userNames[6]}/${userNames[14]}';
    }
    //print(url);
    final response = await http.get(Uri.parse(url));

    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);
      print('diary record');
      print(responseBody['data']);
      final List<Map<String, dynamic>> data = List<Map<String, dynamic>>.from(
        responseBody['data'],
      );

      for (Map<String, dynamic> item in data) {
        String dateKey = item['created_at'].toString();
        if (!_jsonData.containsKey(dateKey)) {
          _jsonData[dateKey] = [];
        }
        _jsonData[dateKey]!.add(item);
      }

      return _jsonData;
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;

        if (userNames.last == '1') {
          setState(() {
            isTeacher = false;
          });
        } else if (userNames.last == '2') {
          setState(() {
            isTeacher = true;
          });
        }
      });
    }
  }

  void dispose() {
    super.dispose();
  }

  void initState() {
    super.initState();

    getListString();
    scrollcontroller.addListener(() {
      if (scrollcontroller.position.maxScrollExtent ==
          scrollcontroller.offset) {
        this.fetchdairyData();
      }
    });

    branch.add(DropdownMenuItem(value: '0', child: Text('Select')));
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: AppColors.primary, // Match the app bar green color
        statusBarIconBrightness:
            Brightness.light, // Light icons for dark status bar
        statusBarBrightness: Brightness.dark, // For iOS
      ),
    );

    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: Container(
            decoration: const BoxDecoration(
              gradient: AppColors.primaryGradient,
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: IconButton(
                onPressed: back,
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: AppColors.textOnPrimary,
                  size: 24,
                ),
              ),
              title: Text(
                'School Diary',
                style: AppTextStyles.headlineSmall.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: FontWeight.w700,
                  fontSize: 22,
                ),
              ),
              centerTitle: true,
            ),
          ),
        ),
        body: mainDairy(),
        bottomNavigationBar: Container(
          height: MediaQuery.of(context).padding.bottom,
          color: AppColors.background,
        ),
      ),
    );
  }

  popup(context) {
    return showDialog(
      context: context,
      builder:
          (BuildContext context) => AlertDialog(
            title: const Text('AlertDialog Title'),
            content: const Text('AlertDialog description'),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.pop(context, 'Cancel'),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, 'OK'),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  Widget mainDairy() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFFAFAFA), Color(0xFFFFFFFF)],
        ),
      ),
      child: FutureBuilder<Map<String, List<Map<String, dynamic>>>>(
        future: fetchdairyData(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(
              child: CircularProgressIndicator(color: AppColors.primary),
            );
          } else if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 15.0.wp,
                    color: AppColors.error,
                  ),
                  SizedBox(height: 2.0.hp),
                  Text(
                    'Something went wrong',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            );
          } else {
            Map<String, List<Map<String, dynamic>>> groupedData =
                snapshot.data!;
            if (groupedData.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.book_outlined,
                      size: 15.0.wp,
                      color: AppColors.textSecondary,
                    ),
                    SizedBox(height: 2.0.hp),
                    Text(
                      'No diary entries found',
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              );
            }

            return ListView.separated(
              padding: EdgeInsets.all(4.0.wp),
              separatorBuilder: (context, index) => SizedBox(height: 2.0.hp),
              itemCount: groupedData.length,
              itemBuilder: (context, index) {
                String dateKey = groupedData.keys.elementAt(index);
                List<Map<String, dynamic>> dataForDate = groupedData[dateKey]!;

                DateTime dateTime;
                if (dateKey.contains('AM') || dateKey.contains('PM')) {
                  DateFormat inputFormat = DateFormat('yyyy-MM-dd hh:mm a');
                  dateTime = inputFormat.parse(dateKey);
                } else {
                  DateFormat inputFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
                  dateTime = inputFormat.parse(dateKey);
                }

                String formattedDate = DateFormat(
                  'd MMM y | EEEE',
                ).format(dateTime);
                String formattedTime = DateFormat('hh:mm a').format(dateTime);

                // Check if this is today's homework
                bool isToday =
                    DateFormat('yyyy-MM-dd').format(dateTime) ==
                    DateFormat('yyyy-MM-dd').format(DateTime.now());

                return Container(
                  margin: EdgeInsets.only(bottom: 2.0.hp),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.cardShadow,
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(4.0.wp),
                        decoration: BoxDecoration(
                          color:
                              isToday
                                  ? AppColors.primary
                                  : const Color(0xFF6B7280),
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(16),
                            topRight: Radius.circular(16),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              isToday ? Icons.today : Icons.calendar_today,
                              color: AppColors.textOnPrimary,
                              size: 4.5.wp,
                            ),
                            SizedBox(width: 2.0.wp),
                            Expanded(
                              child: Text(
                                isToday ? "Today's Homework" : formattedDate,
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: AppColors.textOnPrimary,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            if (!isToday)
                              Text(
                                formattedTime,
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.textOnPrimary.withOpacity(
                                    0.8,
                                  ),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(4.0.wp),
                        child: Column(
                          children:
                              dataForDate.map((item) {
                                var dos = item['given_date'];
                                DateTime postedDate = DateTime.parse(
                                  item['date'],
                                );
                                String postedDates = DateFormat(
                                  'd MMM y, EEEE',
                                ).format(postedDate);

                                return Container(
                                  margin: EdgeInsets.only(bottom: 3.0.hp),
                                  decoration: BoxDecoration(
                                    color: AppColors.background,
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: AppColors.primaryLight.withOpacity(
                                        0.2,
                                      ),
                                      width: 1,
                                    ),
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(12),
                                      onTap: () {
                                        Get.to(
                                          DairySecondScreenDetails(
                                            user_id: item['id'],
                                            title: item['title'],
                                            description: item['description'],
                                            posted_date: item['date'],
                                            ending_date: dos,
                                            teacher_name:
                                                item['role'] == 2
                                                    ? item['staff_name']
                                                    : 'Admin',
                                            url: item['url'] ?? '',
                                          ),
                                        );
                                      },
                                      child: Padding(
                                        padding: EdgeInsets.all(4.0.wp),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Container(
                                                  padding: EdgeInsets.symmetric(
                                                    horizontal: 3.0.wp,
                                                    vertical: 1.0.hp,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: const Color(
                                                      0xFF374151,
                                                    ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          8,
                                                        ),
                                                  ),
                                                  child: Text(
                                                    item['subject_name'],
                                                    style: AppTextStyles
                                                        .bodySmall
                                                        .copyWith(
                                                          color:
                                                              AppColors
                                                                  .textOnPrimary,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                  ),
                                                ),
                                                const Spacer(),
                                                Text(
                                                  item['role'] == 2
                                                      ? item['staff_name']
                                                      : 'Admin',
                                                  style: AppTextStyles.bodySmall
                                                      .copyWith(
                                                        color:
                                                            AppColors
                                                                .textSecondary,
                                                      ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 2.0.hp),
                                            Text(
                                              item['title'],
                                              style: AppTextStyles.titleMedium
                                                  .copyWith(
                                                    color:
                                                        AppColors.textPrimary,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                            ),
                                            SizedBox(height: 1.5.hp),
                                            Row(
                                              children: [
                                                Icon(
                                                  Icons.schedule,
                                                  size: 4.0.wp,
                                                  color:
                                                      AppColors.textSecondary,
                                                ),
                                                SizedBox(width: 1.0.wp),
                                                Text(
                                                  'DOS: $postedDates',
                                                  style: AppTextStyles.bodySmall
                                                      .copyWith(
                                                        color:
                                                            AppColors
                                                                .textSecondary,
                                                      ),
                                                ),
                                                const Spacer(),
                                                Container(
                                                  padding: EdgeInsets.all(
                                                    2.0.wp,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: AppColors
                                                        .primaryLight
                                                        .withOpacity(0.1),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          8,
                                                        ),
                                                  ),
                                                  child: GestureDetector(
                                                    onTap: () {
                                                      Get.to(
                                                        MainPageChat(
                                                          staff_id:
                                                              item['staff_id']
                                                                  .toString(),
                                                          student_id:
                                                              userNames[0],
                                                          Section_name:
                                                              item['section_name'],
                                                          class_name:
                                                              item['class_name'],
                                                          Profile:
                                                              item['profile'],
                                                          name:
                                                              item['staff_name'],
                                                          sender_id:
                                                              userNames[0],
                                                          message_count: 1,
                                                          class_id:
                                                              userNames[6],
                                                          section_id:
                                                              userNames[5],
                                                          branch_id:
                                                              userNames[7],
                                                        ),
                                                      );
                                                    },
                                                    child: Icon(
                                                      Icons.chat_bubble_outline,
                                                      size: 5.0.wp,
                                                      color: AppColors.primary,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          }
        },
      ),
    );
  }
}

class DairySecondScreenDetails extends StatefulWidget {
  final String title;
  final String description;
  final int user_id;
  final String posted_date;
  final String ending_date;
  final String teacher_name;

  final String url;
  DairySecondScreenDetails({
    super.key,
    required this.user_id,
    required this.title,
    required this.description,
    required this.posted_date,
    required this.ending_date,
    required this.teacher_name,
    required this.url,
  });

  @override
  State<DairySecondScreenDetails> createState() =>
      _DairySecondScreenDetailsState();
}

class _DairySecondScreenDetailsState extends State<DairySecondScreenDetails> {
  subback() {
    // Get.to(const MainBoard());
    Get.back();
  }

  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      throw 'Could not launch $url';
    }
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: AppColors.primary, // Match the app bar green color
        statusBarIconBrightness:
            Brightness.light, // Light icons for dark status bar
        statusBarBrightness: Brightness.dark, // For iOS
      ),
    );

    DateTime parsedDate = DateTime.parse(widget.ending_date);
    String formattedDate = DateFormat('d MMM y | EEEE').format(parsedDate);

    DateTime postedDate = DateTime.parse(widget.posted_date);
    String postedDates = DateFormat('d MMM y, EEEE').format(postedDate);

    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: Container(
            decoration: const BoxDecoration(
              gradient: AppColors.primaryGradient,
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: IconButton(
                onPressed: subback,
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: AppColors.textOnPrimary,
                  size: 24,
                ),
              ),
              title: Text(
                'Diary Details',
                style: AppTextStyles.headlineSmall.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: FontWeight.w700,
                  fontSize: 22,
                ),
              ),
              centerTitle: true,
            ),
          ),
        ),
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xFFFAFAFA), Color(0xFFFFFFFF)],
            ),
          ),
          child: SingleChildScrollView(
            padding: EdgeInsets.all(6.0.wp),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 2.0.hp),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(5.0.wp),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.cardShadow,
                        blurRadius: 12,
                        offset: const Offset(0, 6),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 4.0.wp,
                          vertical: 1.5.hp,
                        ),
                        decoration: BoxDecoration(
                          gradient: AppColors.primaryGradient,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.calendar_today,
                              color: AppColors.textOnPrimary,
                              size: 5.0.wp,
                            ),
                            SizedBox(width: 3.0.wp),
                            Text(
                              formattedDate,
                              style: AppTextStyles.titleMedium.copyWith(
                                color: AppColors.textOnPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 4.0.hp),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              widget.title,
                              style: AppTextStyles.headlineSmall.copyWith(
                                color: AppColors.textPrimary,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 3.0.wp,
                              vertical: 1.0.hp,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primaryLight.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              widget.teacher_name,
                              style: AppTextStyles.bodySmall.copyWith(
                                color: AppColors.primary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 3.0.hp),
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(4.0.wp),
                        decoration: BoxDecoration(
                          color: AppColors.background,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: AppColors.primaryLight.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          widget.description,
                          style: AppTextStyles.bodyLarge.copyWith(
                            color: AppColors.textPrimary,
                            height: 1.6,
                          ),
                        ),
                      ),
                      SizedBox(height: 3.0.hp),
                      Row(
                        children: [
                          Icon(
                            Icons.schedule,
                            size: 4.0.wp,
                            color: AppColors.textSecondary,
                          ),
                          SizedBox(width: 2.0.wp),
                          Text(
                            'DOS: $postedDates',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.textSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      if (widget.url.isNotEmpty) ...[
                        SizedBox(height: 3.0.hp),
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(4.0.wp),
                          decoration: BoxDecoration(
                            color: AppColors.primaryLight.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.primary.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.link,
                                    size: 4.0.wp,
                                    color: AppColors.primary,
                                  ),
                                  SizedBox(width: 2.0.wp),
                                  Text(
                                    'Attachment',
                                    style: AppTextStyles.bodyMedium.copyWith(
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 1.0.hp),
                              GestureDetector(
                                onTap: () => _launchUrl(widget.url),
                                child: Text(
                                  widget.url,
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: AppColors.primary,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                SizedBox(height: 3.0.hp),
              ],
            ),
          ),
        ),
        bottomNavigationBar: Container(
          height: MediaQuery.of(context).padding.bottom,
          color: AppColors.background,
        ),
      ),
    );
  }
}
