// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import 'package:intl/date_symbol_data_local.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/main.dart';

import 'package:silverleaf/view/dashboard/mainboard.dart';
import 'package:silverleaf/widgets/chatWidgets.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

var messagescreenpages = 0;

class MessageMainScreen extends StatefulWidget {
  const MessageMainScreen({super.key});

  @override
  State<MessageMainScreen> createState() => _MessageMainScreenState();
}

class _MessageMainScreenState extends State<MessageMainScreen> {
  List userNames = [];
  final StreamController<List<Map<String, dynamic>>> _streamController =
      StreamController<List<Map<String, dynamic>>>();

  var jsonData;
  late bool _isFetchingData;
  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;
        print(userNames);

        if (userNames.last == '1') {
          isTeacher = false;
          this.fetchData();
        } else if (userNames.last == '2') {
          isTeacher = true;
          this.fetchData();
        }
      });
    }
  }

  back() {
    Get.back();
  }

  void initState() {
    super.initState();
    _isFetchingData = true;

    getListString();

    fetchData();
  }

  void dispose() {
    // fetchData();
    _isFetchingData = false;
    _streamController.close();

    super.dispose();
  }

  fetchData() async {
    try {
      while (_isFetchingData) {
        final data = await list_user();

        //final typedData = List<Map<String, dynamic>>.from(data);
        final typedData = data.values.expand((list) => list).toList();

        _streamController.add(typedData);
        await Future.delayed(Duration(seconds: 5));
      }
      // _streamController.add(data)
    } catch (e) {
      // Handle error
      print('Error: $e');
    }
  }

  String extractDate(String timestamp) {
    DateTime dateTime = DateTime.parse(timestamp);
    String formattedDate =
        '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
    return formattedDate;
  }

  Future<Map<String, List<Map<String, dynamic>>>> list_user() async {
    Map<String, List<Map<String, dynamic>>> _jsonData = {};
    Map<String, Map<String, dynamic>> _latestMessages = {};
    Set<String> uniqueMessages = Set<String>();
    //Future<List> list_user() async {
    final url;
    final data;
    var type;
    if (isTeacher == true) {
      type = '1';

      url = 'https://silverleafms.in/silvar_leaf/api/students/view-user';
      data = {
        'class_id': userNames[6],
        'section_id': userNames[5],
        'branch_id': userNames[7],
        'student_id': userNames[0],
        'type': type,
        'year_id': userNames[14],
      };
    } else {
      print('else block');
      type = '2';
      url = 'https://silverleafms.in/silvar_leaf/api/students/view-user';
      data = {
        'class_id': userNames[6],
        'section_id': userNames[5],
        'branch_id': userNames[7],
        'student_id': userNames[0],
        'type': type,
        'year_id': userNames[14],
      };
    }

    final response = await http.post(Uri.parse(url), body: data);
    print('api value all data');
    print(json.decode(response.body));

    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);
      if (responseBody.containsKey('data') &&
          responseBody['data'] != 'empty-value') {
        var response_student = responseBody['data'];

        print('api-response-all');
        print(response_student);
        this.jsonData = responseBody['status'];

        final List<Map<String, dynamic>> data = List<Map<String, dynamic>>.from(
          responseBody['data'],
        );

        for (Map<String, dynamic> item in data) {
          String dateKey =
              item['posted_date'] != null
                  ? extractDate(item['posted_date'].toString())
                  : DateTime.now().toString();
          String messageId = item['id'].toString();
          String uniqueKey = '$dateKey-$messageId';

          if (!uniqueMessages.contains(uniqueKey)) {
            uniqueMessages.add(uniqueKey);

            if (!_jsonData.containsKey(dateKey)) {
              _jsonData[dateKey] = [];
            }
            _jsonData[dateKey]!.add(item);
          }
        }

        return _jsonData;
      } else {
        return _jsonData;
      }
    } else {
      print(response);
      throw Exception('Failed to load data');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (this.jsonData == 'failed') {
      return Scaffold(
        backgroundColor: AppColors.background,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: Container(
            decoration: const BoxDecoration(
              gradient: AppColors.primaryGradient,
            ),
            child: SafeArea(
              bottom: false,
              child: AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  onPressed: back,
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: AppColors.textOnPrimary,
                    size: 24,
                  ),
                ),
                title: Text(
                  'Messages',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.textOnPrimary,
                    fontWeight: FontWeight.w700,
                    fontSize: 22,
                  ),
                ),
                centerTitle: true,
              ),
            ),
          ),
        ),
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xFFFAFAFA), Color(0xFFFFFFFF)],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(color: AppColors.primary),
                SizedBox(height: 2.0.hp),
                Text(
                  'Loading messages...',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomNavigationBar: Container(
          height: MediaQuery.of(context).padding.bottom,
          color: AppColors.background,
        ),
      );
    } else {
      return Scaffold(
        backgroundColor: AppColors.background,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: Container(
            decoration: const BoxDecoration(
              gradient: AppColors.primaryGradient,
            ),
            child: SafeArea(
              bottom: false,
              child: AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  onPressed: back,
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: AppColors.textOnPrimary,
                    size: 24,
                  ),
                ),
                title: Text(
                  'Messages',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.textOnPrimary,
                    fontWeight: FontWeight.w700,
                    fontSize: 22,
                  ),
                ),
                centerTitle: true,
              ),
            ),
          ),
        ),
        body: StreamBuilder<Iterable<Map<String, dynamic>>>(
          stream: _streamController.stream,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              Iterable<Map<String, dynamic>>? data = snapshot.data;
              return FutureBuilder(
                future: Future.delayed(Duration(seconds: 15)),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.done) {
                    Iterable<Map<String, dynamic>>? data = snapshot.data;

                    if (data == null || data.isEmpty) {
                      return Center(child: Text('No message'));
                    } else {
                      return Center(child: CircularProgressIndicator());
                    }
                  } else {
                    return Center(child: CircularProgressIndicator());
                  }
                },
              );
            } else if (snapshot.hasError || snapshot.data == null) {
              return Center(child: CircularProgressIndicator());
            } else {
              Iterable<Map<String, dynamic>> data = snapshot.data!;
              Map<String, List<Map<String, dynamic>>> groupedData = {};
              if (data == null || data.isEmpty) {
                return Center(child: Text('No message'));
              } else {
                for (Map<String, dynamic> item in data) {
                  String formattedDate = '';

                  DateTime parsedDate = DateTime.parse(
                    item['posted_date'].toString(),
                  );

                  formattedDate = DateFormat(
                    'dd MMM yyyy | EEEE',
                  ).format(parsedDate);

                  if (!groupedData.containsKey(formattedDate)) {
                    groupedData[formattedDate] = [];
                  }
                  groupedData[formattedDate]!.add(item);
                }

                return Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Color(0xFFFAFAFA), Color(0xFFFFFFFF)],
                    ),
                  ),
                  child: ListView.separated(
                    padding: EdgeInsets.all(4.0.wp),
                    separatorBuilder: (context, index) {
                      return SizedBox(height: 1.0.hp);
                    },
                    itemCount: groupedData.keys.length,
                    itemBuilder: (context, index) {
                      String dateKey = groupedData.keys.elementAt(index);
                      List<Map<String, dynamic>> dataForDate =
                          groupedData[dateKey]!;

                      return Container(
                        margin: EdgeInsets.only(bottom: 2.0.hp),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Date Header
                            Container(
                              margin: EdgeInsets.only(bottom: 1.0.hp),
                              padding: EdgeInsets.symmetric(
                                horizontal: 4.0.wp,
                                vertical: 1.0.hp,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                dateKey,
                                style: AppTextStyles.bodyMedium.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.primary,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children:
                                  dataForDate.map((item) {
                                    return Container(
                                      margin: EdgeInsets.only(bottom: 1.0.hp),
                                      decoration: BoxDecoration(
                                        color: AppColors.surface,
                                        borderRadius: BorderRadius.circular(12),
                                        boxShadow: [
                                          BoxShadow(
                                            color: AppColors.cardShadow,
                                            blurRadius: 4,
                                            offset: const Offset(0, 1),
                                          ),
                                        ],
                                      ),
                                      child: ListTile(
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: 4.0.wp,
                                          vertical: 1.0.hp,
                                        ),
                                        onTap: () {
                                          Get.to(
                                            MainPageChat(
                                              staff_id:
                                                  userNames.last == '1'
                                                      ? item['id'].toString()
                                                      : userNames[0],
                                              student_id:
                                                  userNames.last == '1'
                                                      ? userNames[0]
                                                      : item['id'].toString(),
                                              Section_name:
                                                  item['section_name'] == null
                                                      ? ''
                                                      : item['section_name'],
                                              class_name:
                                                  item['class_name'] == null
                                                      ? ''
                                                      : item['class_name'],
                                              Profile: item['profile'],
                                              name: item['name'],
                                              sender_id: userNames[0],
                                              message_count: 1,
                                              class_id:
                                                  userNames.last == '1'
                                                      ? userNames[6]
                                                      : userNames[6],
                                              section_id:
                                                  userNames.last == '1'
                                                      ? userNames[5]
                                                      : userNames[5],
                                              branch_id:
                                                  userNames.last == '1'
                                                      ? userNames[7]
                                                      : userNames[7],
                                            ),
                                          );
                                        },
                                        leading: CircleAvatar(
                                          radius: 25.0.sp,
                                          child: ClipRRect(
                                            borderRadius: BorderRadius.circular(
                                              80.0,
                                            ),
                                            child: Image.network(
                                              item['profile'],
                                              width: 160,
                                              height: 160,
                                              fit: BoxFit.cover,
                                              errorBuilder: (
                                                context,
                                                error,
                                                stackTrace,
                                              ) {
                                                return Image.asset(
                                                  'images/user.png',
                                                  width: 160,
                                                  height: 160,
                                                );
                                              },
                                            ),
                                          ),
                                        ),
                                        title: Text(
                                          isTeacher == true
                                              ? "${item['name']}, Std-${item['class_name']}, ${item['section_name']}"
                                              : item['name'],
                                          style: textStyle.copyWith(
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                        subtitle: Text(
                                          "${item['message'] == null ? '' : item['message']}",
                                        ),
                                        trailing: Visibility(
                                          visible:
                                              item['message_count']
                                                          .toString() ==
                                                      '0'
                                                  ? false
                                                  : item['sender_id']
                                                          .toString() ==
                                                      userNames[0]
                                                  ? false
                                                  : true,
                                          child: CircleAvatar(
                                            radius: 13,
                                            backgroundColor: appcolor,
                                            child: Text(
                                              item['message_count'].toString(),
                                              style: textStyle.copyWith(
                                                color: Colors.white,
                                                fontSize: 10.0.sp,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                );
              }
            }
          },
        ),
        bottomNavigationBar: Container(
          height: MediaQuery.of(context).padding.bottom,
          color: AppColors.background,
        ),
      );
    }
  }
}

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        centerTitle: true,
        title: Text("Message"),
        actions: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: SizedBox(
              height: 10.0.hp,
              width: 10.0.wp,
              child: Image.asset("images/profilevector.png"),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Stack(
          children: [
            Container(
              height: double.infinity,
              width: double.infinity,
              color: const Color(0xff7FB85D).withOpacity(0.4),
              child: Column(
                children: [
                  SizedBox(height: 10.0.hp),
                  SizedBox(height: 2.0.hp),
                  Container(
                    height: 3.5.hp,
                    // width: ,
                    width: 25.0.wp,
                    color: Color(0xff12827A),
                    alignment: Alignment.center,
                    child: Text(
                      "Yesterday",
                      style: dairyTextStyle.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.all(20.0.sp),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CircleAvatar(
                          radius: 12.0.sp,
                          backgroundImage: ExactAssetImage(
                            'images/Ellipse 16.png',
                          ),
                        ),
                        SizedBox(width: 2.0.wp),
                        ConstrainedBox(
                          constraints: BoxConstraints(),
                          child: Container(
                            height: 5.0.hp,
                            width: 50.0.wp,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(10),
                                bottomRight: Radius.circular(10),
                                topRight: Radius.circular(10),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.all(20.0.sp),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ConstrainedBox(
                          constraints: BoxConstraints(),
                          child: Container(
                            height: 5.0.hp,
                            width: 50.0.wp,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(10),
                                bottomRight: Radius.circular(10),
                                topRight: Radius.circular(10),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 2.0.wp),
                        CircleAvatar(
                          radius: 12.0.sp,
                          backgroundImage: ExactAssetImage(
                            'images/profilevector.png',
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Container(
              height: 10.0.hp,
              width: double.infinity,
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    blurRadius: 2,
                    blurStyle: BlurStyle.outer,
                    color: appcolor,
                    offset: Offset(0, -1),
                    spreadRadius: 2,
                  ),
                ],
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              padding: EdgeInsets.only(left: 15.0.sp),
              child: Row(
                children: [
                  CircleAvatar(
                    backgroundImage: ExactAssetImage("images/Ellipse 16.png"),
                    radius: 20.0.sp,
                  ),
                  SizedBox(width: 6.0.wp),
                  Text(
                    "Mr. Karthi",
                    style: dairyTextStyle.copyWith(
                      color: Colors.black,
                      fontWeight: FontWeight.w900,
                      fontSize: 12.0.sp,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
